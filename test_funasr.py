import sys
import os

# 添加FunASR-main源码路径
sys.path.insert(0, './FunASR-main')  # 如果FunASR-main在当前目录
# 或者使用绝对路径
# sys.path.insert(0, '/path/to/your/FunASR-main')



from funasr import AutoModel
from funasr.utils.postprocess_utils import rich_transcription_postprocess
import funasr
# 验证是否使用的是源码版本
print(f"FunASR路径: {funasr.__file__}")
print(f"FunASR版本: {funasr.__version__}")

# 如果路径显示的是 ./FunASR-main/funasr/__init__.py，说明成功了
import time
model_dir = "./funasr_models/iic/SenseVoiceSmall"
vad_model_dir = "./funasr_models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch"
input_file = f"./data/像我这样的人-毛不易#hxmnf.mp3"

# 像我这样的人-毛不易#hxmnf.mp3
# 🔍 VAD原始检测段数: 37
# 🔍 VAD原始检测段数: 15

# 🔍 VAD原始检测段数: 36
# 🔍 VAD原始检测段数: 14

#  检测到 38 个语音段
# ✓ 合并后剩余 14 个语音段

# s_time = time.time()
# model = AutoModel(
#     model=model_dir,
#     # trust_remote_code=False,
#     # remote_code="./model.py",  
#     vad_model=vad_model_dir,
#     vad_kwargs={"max_single_segment_time": 30000},
#     device="cpu",
# )
# print(model.model_path)
# load_time = time.time()
# print(f"模型加载时间: {time.time() - s_time:.2f}秒")

# res = model.generate(
#     input=input_file,
#     cache={},
#     language="auto",  # "zn", "en", "yue", "ja", "ko", "nospeech"
#     use_itn=True,
#     # batch_size_s=60,
#     merge_vad=True,  #
#     merge_length_s=15,
# )
# print(res)
# text = rich_transcription_postprocess(res[0]["text"])
# print(text)
# print(f"推理时间: {time.time() - load_time:.2f}秒")




print("***********************AutoModel VAD*********************************")
model = AutoModel(model=vad_model_dir,device="cpu")
print("Frontend类型:", type(model.vad_kwargs.get("frontend")))
print("Frontend配置:", model.vad_kwargs.get("frontend_conf"))
res1 = model.generate(input=input_file)
print(res1)
# python test_funasr.py 
# **********************************************************
# funasr version: 1.2.7.
# Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
# You are using the latest version of funasr-1.2.7
# WARNING:root:trust_remote_code: False
# rtf_avg: 0.019: 100%|██████████████████████████████████████████████████████████████| 1/1 [00:00<00:00,  1.93it/s]
# [{'key': '像我这样的人-毛不易#hxmnf', 'value': 
# [[3770, 4590], [15240, 18010], [19230, 22230], [22970, 29330], [30700, 33320], [34610, 37230], 
# [38460, 45380], [46210, 48920], [50040, 52690], [54000, 60460], [76320, 76940], [79180, 81780], 
# [82960, 85580], [86890, 90470], [90780, 93280], [94600, 97190], [98470, 101260], [102360, 108860], 
# [110060, 112790], [113960, 116540], [117820, 119420], [119840, 124190], [125660, 128240], 
# [129420, 131730], [133300, 139570], [146860, 149020], [150700, 153230], [154590, 156850], 
# [158800, 160860], [162390, 164520], [166230, 167960], [170100, 173410], [174010, 175670], 
# [179740, 183240], [185500, 187380], [190320, 191920], [192200, 193520]]}]
from funasr_onnx import Fsmn_vad
from pathlib import Path
print("***********************funasr_onnx VAD*********************************")
model_dir = "funasr_models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch"
wav_path = "data/像我这样的人-毛不易#hxmnf.mp3"
model = Fsmn_vad(model_dir)
print(model)
onnx_vad_time = time.time()
result = model(wav_path)
print(result)
print("ONNX VAD推理时间:", time.time() - onnx_vad_time)
# python test_funasr.py 
# **********************************************************
# .onnx does not exist, begin to export onnx
# funasr version: 1.2.7.
# Check update of funasr, and it would cost few times. You may disable it by set `disable_update=True` in AutoModel
# You are using the latest version of funasr-1.2.7
# WARNING:root:trust_remote_code: False
# [[[3770, 4500], [15240, 18660], [19230, 22290], [22970, 29320], [30700, 33320], [34610, 37230], 
# [38460, 45430], [46210, 48950], [50040, 52690], [54000, 60460], [76320, 76950], [79180, 81760], 
# [82960, 85590], [86890, 90530], [90820, 93290], [94600, 97160], [98470, 101270], [102360, 108830], 
# [110060, 112730], [113960, 116500], [117820, 119420], [119840, 124210], [125690, 128200], 
# [129440, 131260], [133300, 139530], [146860, 148970], [150730, 153170], [154590, 156810], 
# [158800, 160840], [162390, 164530], [166230, 167920], [170100, 173340], [174030, 175610], 
# [179740, 183240], [185510, 187350], [190320, 191990], [192590, 194000]]]



# #请分析FunASR-main下的源码，新建目录，将fsmn_vad不要使用AutoModel封装的代码推理，而是将fsmn_vad推理代码单独
# 拿出来进行推理一个代码，然后进行转换为onnx一个代码，onnx代码推理一个代码，
# onnx请参考FunASR-main/runtime/python/onnxruntime，最后将onnx转换为openvino一个代码，openvino推理一个代码
# 一共五个代码，一个说明文档，环境激活采用source /media/DataWork/code/openvino_whisper0805/openvino_env/bin/activate


#参考FunASR-main/runtime/python/onnxruntime/funasr_onnx/vad_bin.py中的onnx推理代码class Fsmn_vad，
# 修改3_fsmn_vad_onnx_inference.py的代码

#参考test_funasr.py的代码，这里是将fsmn_vad模型和sensevoicemo模型进行组合进行音频转化为文字，请新建一个代码文件将
#standalone_scripts/4_openvino_inference.py和standalone_scripts/5_fsmn_vad_openvino_inference.py进行组合使用


# from model import SenseVoiceSmall
# from funasr.utils.postprocess_utils import rich_transcription_postprocess

# # model_dir = "iic/SenseVoiceSmall"
# m, kwargs = SenseVoiceSmall.from_pretrained(model=model_dir, device="cuda:0")
# m.eval()

# res = m.inference(
#     data_in=input_file,
#     language="auto", # "zh", "en", "yue", "ja", "ko", "nospeech"
#     use_itn=False,
#     ban_emo_unk=False,
#     **kwargs,
# )

# text = rich_transcription_postprocess(res[0][0]["text"])
# print(text)


#请修改代码fsmn_vad_standalone/6_combined_vad_asr_openvino.py
# 除了使用openvino推理模型使用openvino函数，输入输出前处理，后处理全部调用FunASR-main的函数代码
# 相当于替换了AutoModel加载模型，推理依然采用FunASR-main/funasr/auto/auto_model.py中的inference_with_vad，
# 这样做避免了自己开发的算子函数与源码不一致，也保证了输入输出的一致性，请认真研读分析model.generate





# .🎼像我这样优秀的人。本该灿烂过一生，怎么20多年到头来还在人海里浮沉。像我这样聪明的人。
# 🎼早就告别了单纯，怎么还是用了一段情去换一身伤痕。像我这样迷茫的人，像我这样寻找的人。
# 像我这样碌碌无为的人，你还见过多少人？😔🎼。像我这样庸俗的人，从不喜欢装深沉，怎么偶尔听到老歌时。
# 🎼忽然也慌了神，像我这样懦弱的人，凡事都要留几分。怎么曾经也会为了谁想过奋不顾身。像我这样迷茫的人像我这样寻找的人。
# 🎼像我这样碌碌无为的人，你还见过多少人像我这样孤单的人。像我这样傻的人，像我这样不甘平凡的人，世界上有多少？
# 🎼人像这样迷茫。😔像我这样碌碌无为的人，你还见过多少人像我这样孤单的人像我这样傻。
# 😊🎼像我这样不甘平凡的人，世界上有多少人？像我这样莫名其妙的人，会不会有人心疼？😔


# 🎼.眉看面眼高山，山路一道大湾，河水间是塔衣衫。🎼看他瘦弱的肩，伤心目出的肩，不知经历多少心酸。
# 破旧的鞋子老条尖衣服缝补了几遍。😔那块玻璃是最简单的帆，换水滴落石卷，晕开远方的答。
# 烛光下是他坚定眉眼，月光踏落无檐。🎼淹不在远方的山川，山的山路石弯。他的身影浅浅，却是步步勇敢。
# 你看繁星闪啊闪闪耀在人间。🎼。😊你看那延高山山路一道大弯，河水间是塔衣衫。
# 看他瘦弱的剑，伤心无处的剑，不知经历多少心酸。🎼破旧的写字楼条件衣服缝补了几遍，那块玻璃是最简单的帆，
# 汗水滴落石针晕开远方的答案。烛光下是他坚定眉眼，月光透落无言，落在远方的山川却剩下的少年。
# 🎼绕这山路路十八弯，他的身影渐浅，却是不不勇敢。😊繁星闪啊闪闪耀在人间，月光大照人面，照亮远方的全山。
# 🎼一灯波向前，破下光芒的耀眼，的寒的信念。😊熬过岁月的冷眼，前路中璀璨。


# 🎼  🎼朋友们，晚上好，欢迎大家来参加今天晚上的活动，谢谢大家。😊这是我第四次颁年度演讲。
# 🎼前三次呢，因为疫情的原因都在小米科技园内举办，现场的人很少，这是第四次。我们仔细想了想，
# 我们还是想办一个比较大的聚会，然后呢让我们的新朋友老朋友一起聚一聚。今天的话呢我们就在北京的。
# 🎼国家会议中心呢举办了这么一个活动，现场呢来了很多人，大概有3500人。还有很多很多的朋友呢通过观看直播的方式来参与。
# 再一次呢对大家的参加表示感谢，谢谢大家。😊🎼两个月前，我参加了今年武汉大学的毕业典礼。
# 今年呢是武汉大学建校130周年，作为校友被母校邀请，在毕业典礼上致辞。🎼这对我来说是至高无上的荣誉。
# 站在讲台的那一刻，面对全校师生。关于武大的所有的记忆，一下子涌现在脑海里。今天呢我就先和大家聊聊五大往事。
# 😡🎼那还是36年前，1987年，我呢考上了武汉大学的计算机系。在武汉大学的图书馆里看了一本书，硅谷之火，
# 建立了我一生的梦想。看完书以后，热血沸腾。🎼激动的睡不着觉，我还记得那天晚上星光很亮，我就在五大的操场上，
# 就是屏幕上这个操场。走了一圈又一圈，走了整整一个晚上，我心里有团火，我也想搬一个伟大的公司，就是这样。
# 🎼梦想之火在我心里彻底点燃了。😡但是一个大一的新生，但是一个大一的新生。🎼一个从县城里出来的年轻人，
# 什么也不会，什么也没有，就想创办一家伟大的公司，这不就是天荒夜谭吗？这么离谱的一个梦想，该如何实现呢？
# 那天晚上我想了一整晚上，说实话。🎼越想越糊涂，完全理不清头绪。后来我在想，
# 哎，干脆别想了，把书练好是正事。所以呢我就下定决心认认真真读书。那么我怎么能够把书读的不同凡响呢？😡



# 请基于/media/DataWork/code/sensevoice0825/fsmn_vad_standalone/9_funasr_openvino_official0827.py代码流程，
# 将python代码修改为C++代码，
# C++请参考/media/DataWork/code/sensevoice0825/FunASR-main/runtime/onnxruntime/src有sensevoice-small.cpp和
# sensevoice-small.h，fsmn-vad.cpp和fsmn-vad.h，
# /media/DataWork/code/sensevoice0825/FunASR-main/runtime/onnxruntime/bin/funasr-onnx-offline.cpp和funasr-onnx-offline-vad.cpp，
# 上面这些CPP代码对应python代码中流程，只是将推理模型onnx换成openivino

# 请认真分析/media/DataWork/code/sensevoice0825/FunASR-main/runtime/onnxruntime下的代码
# 我按照readme进行编译报错如下：-- Using src='https://github.com/nlohmann/json/archive/refs/tags/v3.11.2.tar.gz'
# CMake Error at /media/DataWork/code/sensevoice0825/FunASR-main/runtime/onnxruntime/build/_deps/json-subbuild/json-populate-prefix/src/json-populate-stamp/download-json-populate.cmake:170 (message):
#   Each download failed!

#     error: downloading 'https://github.com/nlohmann/json/archive/refs/tags/v3.11.2.tar.gz' failed
#           status_code: 28
#           status_string: "Timeout was reached"
#           log:
#           --- LOG BEGIN ---
#             Trying 20.205.243.166:443...

#   connect to 20.205.243.166 port 443 failed: Connection timed out

#   Failed to connect to github.com port 443 after 135725 ms: Connection timed
#   out

#   Closing connection 0

  

#           --- LOG END ---
          
    


# gmake[2]: *** [CMakeFiles/json-populate.dir/build.make:99：json-populate-prefix/src/json-populate-stamp/json-populate-download] 错误 1
# gmake[1]: *** [CMakeFiles/Makefile2:83：CMakeFiles/json-populate.dir/all] 错误 2
# gmake: *** [Makefile:91：all] 错误 2

# CMake Error at /usr/share/cmake-3.22/Modules/FetchContent.cmake:1087 (message):
#   Build step for json failed: 2
# Call Stack (most recent call first):
#   /usr/share/cmake-3.22/Modules/FetchContent.cmake:1216:EVAL:2 (__FetchContent_directPopulate)
#   /usr/share/cmake-3.22/Modules/FetchContent.cmake:1216 (cmake_language)
#   /usr/share/cmake-3.22/Modules/FetchContent.cmake:1259 (FetchContent_Populate)
#   CMakeLists.txt:30 (FetchContent_MakeAvailable)


# -- Configuring incomplete, errors occurred!
# See also "/media/DataWork/code/sensevoice0825/FunASR-main/runtime/onnxruntime/build/CMakeFiles/CMakeOutput.log".