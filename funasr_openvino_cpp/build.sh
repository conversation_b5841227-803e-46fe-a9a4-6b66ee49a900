#!/bin/bash

# FunASR OpenVINO C++ Build Script
# Based on Python implementation: 9_funasr_openvino_official0827.py

set -e

echo "============================================================"
echo "FunASR OpenVINO C++ Build Script"
echo "============================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "CMakeLists.txt" ]; then
    print_error "CMakeLists.txt not found. Please run this script from the funasr_openvino_cpp directory."
    exit 1
fi

# Parse command line arguments
BUILD_TYPE="Release"
CLEAN_BUILD=false
INSTALL=false
JOBS=$(nproc)

while [[ $# -gt 0 ]]; do
    case $1 in
        --debug)
            BUILD_TYPE="Debug"
            shift
            ;;
        --clean)
            CLEAN_BUILD=true
            shift
            ;;
        --install)
            INSTALL=true
            shift
            ;;
        --jobs)
            JOBS="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --debug     Build in Debug mode (default: Release)"
            echo "  --clean     Clean build directory before building"
            echo "  --install   Install after building"
            echo "  --jobs N    Number of parallel jobs (default: $(nproc))"
            echo "  --help      Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information."
            exit 1
            ;;
    esac
done

print_status "Build configuration:"
echo "  Build type: $BUILD_TYPE"
echo "  Clean build: $CLEAN_BUILD"
echo "  Install: $INSTALL"
echo "  Parallel jobs: $JOBS"
echo ""

# Check dependencies
print_status "Checking dependencies..."

# Check CMake
if ! command -v cmake &> /dev/null; then
    print_error "CMake not found. Please install CMake >= 3.16"
    exit 1
fi

CMAKE_VERSION=$(cmake --version | head -n1 | cut -d' ' -f3)
print_success "CMake found: $CMAKE_VERSION"

# Check pkg-config
if ! command -v pkg-config &> /dev/null; then
    print_error "pkg-config not found. Please install pkg-config"
    exit 1
fi
print_success "pkg-config found"

# Check libsndfile
if ! pkg-config --exists sndfile; then
    print_error "libsndfile not found. Please install libsndfile1-dev"
    exit 1
fi
print_success "libsndfile found"

# Check yaml-cpp (this might not be available via pkg-config on all systems)
print_status "yaml-cpp will be checked during CMake configuration"

# Check OpenVINO
if [ -z "$INTEL_OPENVINO_DIR" ] && [ -z "$OpenVINO_DIR" ]; then
    print_warning "OpenVINO environment variables not set. Make sure OpenVINO is properly installed and sourced."
    print_warning "You may need to run: source /opt/intel/openvino/setupvars.sh"
fi

# Create and enter build directory
BUILD_DIR="build"

if [ "$CLEAN_BUILD" = true ] && [ -d "$BUILD_DIR" ]; then
    print_status "Cleaning build directory..."
    rm -rf "$BUILD_DIR"
fi

if [ ! -d "$BUILD_DIR" ]; then
    print_status "Creating build directory..."
    mkdir "$BUILD_DIR"
fi

cd "$BUILD_DIR"

# Configure with CMake
print_status "Configuring with CMake..."
cmake .. -DCMAKE_BUILD_TYPE="$BUILD_TYPE"

if [ $? -ne 0 ]; then
    print_error "CMake configuration failed"
    exit 1
fi

print_success "CMake configuration completed"

# Build
print_status "Building with $JOBS parallel jobs..."
make -j"$JOBS"

if [ $? -ne 0 ]; then
    print_error "Build failed"
    exit 1
fi

print_success "Build completed successfully"

# Install if requested
if [ "$INSTALL" = true ]; then
    print_status "Installing..."
    sudo make install
    
    if [ $? -ne 0 ]; then
        print_error "Installation failed"
        exit 1
    fi
    
    print_success "Installation completed"
fi

# Show build results
print_status "Build results:"
echo "  Executable: $(pwd)/funasr_openvino_main"
echo "  Library: $(pwd)/libfunasr_openvino_lib.a"

# Test if executable was created
if [ -f "funasr_openvino_main" ]; then
    print_success "Executable created successfully"
    echo ""
    print_status "You can now run the program with:"
    echo "  cd $(pwd)"
    echo "  ./funasr_openvino_main --help"
else
    print_error "Executable not found after build"
    exit 1
fi

echo ""
print_success "Build process completed successfully!"
echo "============================================================"
