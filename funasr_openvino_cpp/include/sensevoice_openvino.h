#pragma once

#include "common.h"
#include <sentencepiece_processor.h>

namespace funasr_openvino {

class SentencePieceTokenizer {
private:
    std::string model_path_;
    sentencepiece::SentencePieceProcessor processor_;
    bool is_loaded_;

public:
    SentencePieceTokenizer(const std::string& model_path);
    ~SentencePieceTokenizer() = default;

    std::string Decode(const std::vector<int>& token_ids);
    std::vector<int> Encode(const std::string& text);
    bool IsLoaded() const { return is_loaded_; }

private:
    void LoadModel();
};

class SenseVoiceWavFrontend {
private:
    std::vector<float> means_list_;
    std::vector<float> vars_list_;
    int lfr_m_;
    int lfr_n_;
    int n_mels_;
    
public:
    SenseVoiceWavFrontend(const std::string& cmvn_file, int lfr_m = 7, int lfr_n = 6, int n_mels = 80);
    ~SenseVoiceWavFrontend() = default;
    
    // Extract fbank features
    std::pair<std::vector<std::vector<float>>, int> Fbank(const std::vector<float>& waveform);
    
    // Apply LFR and CMVN
    std::pair<std::vector<std::vector<float>>, int> LfrCmvn(const std::vector<std::vector<float>>& speech);
    
private:
    void LoadCMVN(const std::string& cmvn_file);
    std::vector<std::vector<float>> ExtractFbank(const std::vector<float>& waveform);
    std::vector<std::vector<float>> ApplyLFR(const std::vector<std::vector<float>>& features);
    void ApplyCMVN(std::vector<std::vector<float>>& features);
};

class SenseVoiceOpenVINO {
private:
    std::string model_path_;
    std::string device_;
    
    // OpenVINO components
    ov::Core core_;
    std::shared_ptr<ov::Model> model_;
    ov::CompiledModel compiled_model_;
    
    // Frontend and tokenizer
    std::unique_ptr<SenseVoiceWavFrontend> frontend_;
    std::unique_ptr<SentencePieceTokenizer> tokenizer_;
    
    // Language and text normalization mappings
    std::map<std::string, int> lid_dict_;
    std::map<std::string, int> textnorm_dict_;
    
public:
    SenseVoiceOpenVINO(const std::string& model_path, const std::string& device = "CPU");
    ~SenseVoiceOpenVINO() = default;
    
    // Single file inference
    std::string InferenceAudioFile(const std::string& audio_path, const std::string& language = "auto");
    
    // Batch inference for audio segments
    std::vector<std::string> InferenceBatchDirect(
        const std::vector<std::vector<float>>& audio_segments,
        const std::string& language = "auto"
    );
    
    // Fallback batch inference
    std::vector<std::string> InferenceBatchFallback(
        const std::vector<std::vector<float>>& audio_segments,
        const std::string& language = "auto"
    );
    
private:
    void LoadModel();
    void LoadFrontendAndTokenizer();
    
    // Feature extraction
    std::pair<std::vector<std::vector<float>>, std::vector<int>> ExtractFeatures(
        const std::string& audio_path
    );
    
    std::pair<std::vector<std::vector<std::vector<float>>>, std::vector<int>> ExtractFeaturesBatch(
        const std::vector<std::vector<float>>& audio_segments
    );
    
    // Post-processing
    std::string PostProcess(const std::vector<float>& logits, int length);
    std::vector<int> CTCDecode(const std::vector<float>& logits, int length);
    std::vector<int> RemoveDuplicates(const std::vector<int>& tokens);
    std::vector<int> RemoveBlankTokens(const std::vector<int>& tokens, int blank_id = 0);
};

} // namespace funasr_openvino
