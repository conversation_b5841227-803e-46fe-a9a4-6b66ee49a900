#pragma once

#include "common.h"

namespace funasr_openvino {

class WavFrontend {
private:
    std::vector<float> means_list_;
    std::vector<float> vars_list_;
    int lfr_m_;
    int lfr_n_;
    int n_mels_;
    
public:
    WavFrontend(const std::string& cmvn_file, int lfr_m = 5, int lfr_n = 1, int n_mels = 80);
    ~WavFrontend() = default;
    
    // Extract fbank features
    std::pair<std::vector<std::vector<float>>, int> Fbank(const std::vector<float>& waveform);
    
    // Apply LFR and CMVN
    std::pair<std::vector<std::vector<float>>, int> LfrCmvn(const std::vector<std::vector<float>>& speech);
    
private:
    void LoadCMVN(const std::string& cmvn_file);
    std::vector<std::vector<float>> ExtractFbank(const std::vector<float>& waveform);
    std::vector<std::vector<float>> ApplyLFR(const std::vector<std::vector<float>>& features);
    void ApplyCMVN(std::vector<std::vector<float>>& features);
};

class E2EVadModel {
private:
    VADConfig config_;
    std::vector<std::vector<int>> segments_;
    std::vector<float> scores_cache_;
    bool is_final_;
    
public:
    E2EVadModel(const VADConfig& config);
    ~E2EVadModel() = default;
    
    std::vector<std::vector<std::vector<int>>> operator()(
        const std::vector<std::vector<std::vector<float>>>& scores,
        const std::vector<std::vector<float>>& waveform_batch,
        bool is_final = false,
        int max_end_sil = 800,
        bool online = false
    );
    
private:
    std::vector<std::vector<int>> PostProcess(
        const std::vector<std::vector<float>>& scores,
        const std::vector<float>& waveform,
        bool is_final,
        int max_end_sil
    );
    
    std::vector<std::vector<int>> MergeSegments(
        const std::vector<std::vector<int>>& segments,
        int max_length = 15000,
        int min_length = 0
    );
};

class FSMNVADOpenVINO {
private:
    std::string model_dir_;
    std::string device_;
    int batch_size_;
    
    // OpenVINO components
    ov::Core core_;
    std::shared_ptr<ov::Model> model_;
    ov::CompiledModel compiled_model_;
    
    // Frontend and config
    std::unique_ptr<WavFrontend> frontend_;
    VADConfig config_;
    
    // Cache for FSMN layers
    std::vector<ov::Tensor> cache_tensors_;
    
public:
    FSMNVADOpenVINO(const std::string& model_dir, const std::string& device = "CPU", int batch_size = 1);
    ~FSMNVADOpenVINO() = default;
    
    // Main inference interface (compatible with FunASR)
    std::vector<VADResult> Generate(const std::string& input_audio);
    
private:
    void LoadModel();
    void LoadConfig();
    void InitializeCache();
    
    // Feature extraction
    std::pair<std::vector<std::vector<std::vector<float>>>, std::vector<int>> ExtractFeatures(
        const std::vector<std::vector<float>>& waveform_list
    );
    
    // Batch inference
    std::vector<std::vector<std::vector<float>>> RunInference(
        const std::vector<std::vector<std::vector<float>>>& features,
        std::vector<ov::Tensor>& cache
    );
    
    // Padding utilities
    static std::vector<std::vector<std::vector<float>>> PadFeatures(
        const std::vector<std::vector<std::vector<float>>>& features,
        int max_length
    );
};

} // namespace funasr_openvino
