#pragma once

#include <iostream>
#include <vector>
#include <string>
#include <memory>
#include <map>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <chrono>

// OpenVINO includes
#include <openvino/openvino.hpp>

// Audio processing includes
#include <sndfile.h>

// FFmpeg includes for MP3/MP4 support
extern "C" {
#include <libavformat/avformat.h>
#include <libavcodec/avcodec.h>
#include <libavutil/avutil.h>
#include <libswresample/swresample.h>
}

// YAML config parsing
#include <yaml-cpp/yaml.h>

namespace funasr_openvino {

// Common data structures
struct AudioSegment {
    int start_ms;
    int end_ms;
    std::vector<float> audio_data;
};

struct VADResult {
    std::string key;
    std::vector<std::vector<int>> segments;  // [[start_ms, end_ms], ...]
};

struct ASRResult {
    std::string key;
    std::string text;
    std::vector<std::vector<int>> timestamp;
};

// Configuration structures
struct VADConfig {
    int sample_rate = 16000;
    int max_end_silence_time = 800;
    int max_single_segment_time = 60000;
    float speech_noise_thres = 0.6;
    int lfr_m = 5;
    int lfr_n = 1;
    int fsmn_layers = 4;
    int proj_dim = 128;
    int lorder = 10;
};

struct ASRConfig {
    int sample_rate = 16000;
    int lfr_m = 7;
    int lfr_n = 6;
    int n_mels = 80;
    std::string language = "zh";
    bool with_itn = true;
};

// FunASR官方Audio类的简化版本
class FunASRAudio {
private:
    float* speech_data = nullptr;
    int speech_len = 0;
    int dest_sample_rate = 16000;
    int data_type = 1;  // 1 for normalized float

public:
    FunASRAudio(int sample_rate = 16000, int dtype = 1);
    ~FunASRAudio();

    // 官方FFmpeg加载接口
    bool FfmpegLoad(const char* filename, bool copy2char = false);
    bool FfmpegLoad(const char* buf, int n_file_len);

    // 获取音频数据
    float* GetSpeechData() { return speech_data; }
    int GetSpeechLen() { return speech_len; }

    // 转换为vector格式
    std::vector<float> ToVector();

private:
    void ClearSpeechData();
};

// Utility functions
class AudioUtils {
public:
    static std::vector<float> LoadAudio(const std::string& file_path, int target_sr = 16000);
    static void SaveAudio(const std::string& file_path, const std::vector<float>& audio_data, int sample_rate);
    static std::vector<float> Resample(const std::vector<float>& audio, int orig_sr, int target_sr);

private:
    // FFmpeg-based audio loading for MP3/MP4 support
    static std::vector<float> LoadAudioFFmpeg(const std::string& file_path, int target_sr = 16000);
    // libsndfile-based audio loading for WAV support
    static std::vector<float> LoadAudioSndfile(const std::string& file_path, int target_sr = 16000);
    // Check if file format is supported by libsndfile
    static bool IsSndfileSupported(const std::string& file_path);
};

class ConfigUtils {
public:
    static VADConfig LoadVADConfig(const std::string& config_file);
    static ASRConfig LoadASRConfig(const std::string& config_file);
    static std::vector<float> LoadCMVN(const std::string& cmvn_file);
};

// Timer utility
class Timer {
private:
    std::chrono::high_resolution_clock::time_point start_time;
    
public:
    void Start() {
        start_time = std::chrono::high_resolution_clock::now();
    }
    
    double ElapsedSeconds() {
        auto end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
        return duration.count() / 1000000.0;
    }
};

} // namespace funasr_openvino
