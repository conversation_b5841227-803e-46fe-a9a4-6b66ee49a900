#pragma once

#include "common.h"
#include "fsmn_vad_openvino.h"
#include "sensevoice_openvino.h"

namespace funasr_openvino {

struct InferenceConfig {
    // VAD parameters
    bool skip_vad = false;
    bool merge_vad = false;
    int merge_length_s = 15;
    int max_single_segment_time = 60000;
    
    // ASR parameters
    std::string language = "auto";
    int batch_size_s = 300;
    int batch_size_threshold_s = 60;
    
    // Device settings
    std::string device = "CPU";
};

class FunASROpenVINOSystem {
private:
    // Model components
    std::unique_ptr<FSMNVADOpenVINO> vad_model_;
    std::unique_ptr<SenseVoiceOpenVINO> asr_model_;
    
    // Configuration
    InferenceConfig config_;
    
    // Timing
    Timer timer_;
    
public:
    FunASROpenVINOSystem(
        const std::string& vad_model_dir,
        const std::string& asr_model_path,
        const std::string& device = "CPU"
    );
    
    ~FunASROpenVINOSystem() = default;
    
    // Main inference interface (compatible with FunASR official inference_with_vad)
    std::vector<ASRResult> InferenceWithVAD(
        const std::string& input_audio,
        const InferenceConfig& config = InferenceConfig{}
    );

    // Direct ASR inference without VAD (for testing)
    std::vector<ASRResult> DirectASRInference(
        const std::string& input_audio,
        const InferenceConfig& config = InferenceConfig{}
    );
    
private:
    // Data preparation (from funasr.auto.auto_model)
    std::pair<std::vector<std::string>, std::vector<std::string>> PrepareDataIterator(
        const std::string& input_data
    );
    
    // VAD utilities (from funasr.utils.vad_utils)
    std::vector<std::vector<int>> MergeVAD(
        const std::vector<std::vector<int>>& vad_result,
        int max_length = 15000,
        int min_length = 0
    );
    
    std::pair<std::vector<std::vector<float>>, std::vector<int>> SlicePaddingAudioSamples(
        const std::vector<float>& speech,
        int speech_length,
        const std::vector<std::vector<int>>& vad_segments
    );
    
    // Batch processing strategies
    std::vector<std::string> ProcessCPUMode(
        const std::vector<std::vector<int>>& vad_segments,
        const std::vector<float>& speech,
        int speech_length,
        const InferenceConfig& config
    );
    
    std::vector<std::string> ProcessGPUMode(
        const std::vector<std::vector<int>>& vad_segments,
        const std::vector<float>& speech,
        int speech_length,
        const InferenceConfig& config
    );
    
    // Text merging utilities
    std::string MergeTexts(const std::vector<std::string>& texts);
    
    // Audio segment extraction
    std::vector<std::vector<float>> ExtractAudioSegments(
        const std::vector<float>& speech,
        const std::vector<std::vector<int>>& vad_segments,
        int sample_rate = 16000
    );
};

// Utility functions for compatibility with FunASR
namespace utils {

// Load audio (from funasr.utils.load_utils)
std::vector<float> LoadAudioTextImageVideo(
    const std::string& input_path,
    int fs = 16000,
    int audio_fs = 16000
);

// Rich transcription post-processing
std::string RichTranscriptionPostprocess(const std::string& text);

} // namespace utils

} // namespace funasr_openvino
