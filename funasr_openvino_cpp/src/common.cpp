#include "../include/common.h"
#include <cstring>

namespace funasr_openvino {

// AudioUtils implementation
std::vector<float> AudioUtils::LoadAudio(const std::string& file_path, int target_sr) {
    // 首先尝试使用libsndfile（支持WAV等格式）
    if (IsSndfileSupported(file_path)) {
        std::cout << "使用libsndfile加载音频: " << file_path << std::endl;
        return LoadAudioSndfile(file_path, target_sr);
    } else {
        // 使用FunASR官方FFmpeg实现
        std::cout << "使用FunASR官方FFmpeg加载音频: " << file_path << std::endl;
        FunASRAudio audio(target_sr, 1);
        if (audio.FfmpegLoad(file_path.c_str(), false)) {
            return audio.ToVector();
        } else {
            std::cerr << "Error: Failed to load audio with FunASR FFmpeg" << std::endl;
            return {};
        }
    }
}

bool AudioUtils::IsSndfileSupported(const std::string& file_path) {
    // 检查文件扩展名
    std::string ext = file_path.substr(file_path.find_last_of('.') + 1);
    std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);

    // libsndfile支持的格式
    return (ext == "wav" || ext == "flac" || ext == "ogg" || ext == "aiff");
}

std::vector<float> AudioUtils::LoadAudioSndfile(const std::string& file_path, int target_sr) {
    SF_INFO sf_info;
    memset(&sf_info, 0, sizeof(sf_info));

    SNDFILE* file = sf_open(file_path.c_str(), SFM_READ, &sf_info);
    if (!file) {
        std::cerr << "Error: Cannot open audio file with libsndfile: " << file_path << std::endl;
        return {};
    }

    std::cout << "音频文件信息: " << sf_info.frames << " 帧, "
              << sf_info.channels << " 声道, " << sf_info.samplerate << " Hz" << std::endl;

    // Read audio data
    std::vector<float> audio_data(sf_info.frames * sf_info.channels);
    sf_count_t frames_read = sf_readf_float(file, audio_data.data(), sf_info.frames);
    sf_close(file);

    if (frames_read != sf_info.frames) {
        std::cerr << "Warning: Expected " << sf_info.frames << " frames, but read " << frames_read << std::endl;
    }

    // Convert to mono if stereo
    if (sf_info.channels > 1) {
        std::vector<float> mono_data(sf_info.frames);
        for (int i = 0; i < sf_info.frames; ++i) {
            float sum = 0.0f;
            for (int ch = 0; ch < sf_info.channels; ++ch) {
                sum += audio_data[i * sf_info.channels + ch];
            }
            mono_data[i] = sum / sf_info.channels;
        }
        audio_data = std::move(mono_data);
    }

    // Check audio range and normalize if needed (FunASR expects specific range)
    if (!audio_data.empty()) {
        float min_val = *std::min_element(audio_data.begin(), audio_data.end());
        float max_val = *std::max_element(audio_data.begin(), audio_data.end());
        std::cout << "音频范围: [" << min_val << ", " << max_val << "]" << std::endl;

        // If audio is in int16 range, normalize to [-1, 1]
        if (max_val > 1.0f || min_val < -1.0f) {
            float scale = std::max(std::abs(max_val), std::abs(min_val));
            for (float& sample : audio_data) {
                sample /= scale;
            }
            std::cout << "音频已归一化到 [-1, 1] 范围" << std::endl;
        }
    }

    // Resample if necessary
    if (sf_info.samplerate != target_sr) {
        audio_data = Resample(audio_data, sf_info.samplerate, target_sr);
        std::cout << "音频已重采样到 " << target_sr << " Hz" << std::endl;
    }

    return audio_data;
}

std::vector<float> AudioUtils::LoadAudioFFmpeg(const std::string& file_path, int target_sr) {
    std::vector<float> audio_data;

    // 初始化FFmpeg（只需要初始化一次）
    static bool ffmpeg_initialized = false;
    if (!ffmpeg_initialized) {
        av_register_all();
        ffmpeg_initialized = true;
    }

    AVFormatContext* format_ctx = nullptr;
    AVCodecContext* codec_ctx = nullptr;
    AVCodec* codec = nullptr;
    SwrContext* swr_ctx = nullptr;

    try {
        // 打开输入文件
        if (avformat_open_input(&format_ctx, file_path.c_str(), nullptr, nullptr) < 0) {
            std::cerr << "Error: Cannot open audio file with FFmpeg: " << file_path << std::endl;
            return {};
        }

        // 获取流信息
        if (avformat_find_stream_info(format_ctx, nullptr) < 0) {
            std::cerr << "Error: Cannot find stream info" << std::endl;
            avformat_close_input(&format_ctx);
            return {};
        }

        // 找到音频流
        int audio_stream_index = -1;
        for (unsigned int i = 0; i < format_ctx->nb_streams; i++) {
            if (format_ctx->streams[i]->codecpar->codec_type == AVMEDIA_TYPE_AUDIO) {
                audio_stream_index = i;
                break;
            }
        }

        if (audio_stream_index == -1) {
            std::cerr << "Error: No audio stream found" << std::endl;
            avformat_close_input(&format_ctx);
            return {};
        }

        // 获取解码器
        AVCodecParameters* codecpar = format_ctx->streams[audio_stream_index]->codecpar;
        codec = avcodec_find_decoder(codecpar->codec_id);
        if (!codec) {
            std::cerr << "Error: Codec not found" << std::endl;
            avformat_close_input(&format_ctx);
            return {};
        }

        // 创建解码器上下文
        codec_ctx = avcodec_alloc_context3(codec);
        if (!codec_ctx) {
            std::cerr << "Error: Cannot allocate codec context" << std::endl;
            avformat_close_input(&format_ctx);
            return {};
        }

        // 复制解码器参数
        if (avcodec_parameters_to_context(codec_ctx, codecpar) < 0) {
            std::cerr << "Error: Cannot copy codec parameters" << std::endl;
            avcodec_free_context(&codec_ctx);
            avformat_close_input(&format_ctx);
            return {};
        }

        // 打开解码器
        if (avcodec_open2(codec_ctx, codec, nullptr) < 0) {
            std::cerr << "Error: Cannot open codec" << std::endl;
            avcodec_free_context(&codec_ctx);
            avformat_close_input(&format_ctx);
            return {};
        }

        std::cout << "FFmpeg音频信息: " << codec_ctx->sample_rate << " Hz, "
                  << codec_ctx->channels << " 声道" << std::endl;

        // 设置重采样器（如果需要）
        if (codec_ctx->sample_rate != target_sr || codec_ctx->channels != 1 ||
            codec_ctx->sample_fmt != AV_SAMPLE_FMT_FLT) {

            swr_ctx = swr_alloc_set_opts(nullptr,
                                        AV_CH_LAYOUT_MONO,     // 输出：单声道
                                        AV_SAMPLE_FMT_FLT,     // 输出：float格式
                                        target_sr,             // 输出：目标采样率
                                        av_get_default_channel_layout(codec_ctx->channels), // 输入：原声道布局
                                        codec_ctx->sample_fmt, // 输入：原格式
                                        codec_ctx->sample_rate,// 输入：原采样率
                                        0, nullptr);

            if (!swr_ctx || swr_init(swr_ctx) < 0) {
                std::cerr << "Error: Cannot initialize resampler" << std::endl;
                if (swr_ctx) swr_free(&swr_ctx);
                avcodec_free_context(&codec_ctx);
                avformat_close_input(&format_ctx);
                return {};
            }
        }

        // 读取和解码音频数据
        AVPacket packet;
        AVFrame* frame = av_frame_alloc();

        while (av_read_frame(format_ctx, &packet) >= 0) {
            if (packet.stream_index == audio_stream_index) {
                if (avcodec_send_packet(codec_ctx, &packet) >= 0) {
                    while (avcodec_receive_frame(codec_ctx, frame) >= 0) {
                        if (swr_ctx) {
                            // 需要重采样
                            int out_samples = swr_get_out_samples(swr_ctx, frame->nb_samples);
                            float* output_buffer = new float[out_samples];

                            int converted = swr_convert(swr_ctx,
                                                      (uint8_t**)&output_buffer, out_samples,
                                                      (const uint8_t**)frame->data, frame->nb_samples);

                            if (converted > 0) {
                                audio_data.insert(audio_data.end(), output_buffer, output_buffer + converted);
                            }

                            delete[] output_buffer;
                        } else {
                            // 不需要重采样，直接复制
                            float* data = (float*)frame->data[0];
                            int samples = frame->nb_samples * codec_ctx->channels;

                            // 转换为单声道
                            if (codec_ctx->channels == 1) {
                                audio_data.insert(audio_data.end(), data, data + samples);
                            } else {
                                for (int i = 0; i < frame->nb_samples; i++) {
                                    float sum = 0.0f;
                                    for (int ch = 0; ch < codec_ctx->channels; ch++) {
                                        sum += data[i * codec_ctx->channels + ch];
                                    }
                                    audio_data.push_back(sum / codec_ctx->channels);
                                }
                            }
                        }
                    }
                }
            }
            av_packet_unref(&packet);
        }

        // 清理资源
        av_frame_free(&frame);
        if (swr_ctx) swr_free(&swr_ctx);
        avcodec_free_context(&codec_ctx);
        avformat_close_input(&format_ctx);

        std::cout << "FFmpeg加载完成，音频长度: " << audio_data.size() << " 采样点" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "FFmpeg加载异常: " << e.what() << std::endl;
        // 清理资源
        if (swr_ctx) swr_free(&swr_ctx);
        if (codec_ctx) avcodec_free_context(&codec_ctx);
        if (format_ctx) avformat_close_input(&format_ctx);
        return {};
    }

    return audio_data;
}

// FunASRAudio implementation - 移植官方FFmpeg代码
FunASRAudio::FunASRAudio(int sample_rate, int dtype)
    : dest_sample_rate(sample_rate), data_type(dtype) {
}

FunASRAudio::~FunASRAudio() {
    ClearSpeechData();
}

void FunASRAudio::ClearSpeechData() {
    if (speech_data != nullptr) {
        free(speech_data);
        speech_data = nullptr;
    }
    speech_len = 0;
}

std::vector<float> FunASRAudio::ToVector() {
    if (speech_data == nullptr || speech_len == 0) {
        return {};
    }
    return std::vector<float>(speech_data, speech_data + speech_len);
}

// 直接移植官方的FfmpegLoad实现
bool FunASRAudio::FfmpegLoad(const char* filename, bool copy2char) {
#if defined(__APPLE__)
    return false;
#else
    // 清理之前的数据
    ClearSpeechData();

    // from file
    AVFormatContext* formatContext = avformat_alloc_context();
    if (avformat_open_input(&formatContext, filename, nullptr, nullptr) != 0) {
        std::cerr << "Error: Could not open input file." << std::endl;
        avformat_close_input(&formatContext);
        avformat_free_context(formatContext);
        return false;
    }

    if (avformat_find_stream_info(formatContext, nullptr) < 0) {
        std::cerr << "Error: Could not find stream information." << std::endl;
        avformat_close_input(&formatContext);
        avformat_free_context(formatContext);
        return false;
    }

    AVCodec* codec = nullptr;
    AVCodecParameters* codecParameters = nullptr;
    int audioStreamIndex = av_find_best_stream(formatContext, AVMEDIA_TYPE_AUDIO, -1, -1, &codec, 0);
    if (audioStreamIndex >= 0) {
        codecParameters = formatContext->streams[audioStreamIndex]->codecpar;
    } else {
        std::cerr << "Error: No audio stream found." << std::endl;
        avformat_close_input(&formatContext);
        avformat_free_context(formatContext);
        return false;
    }

    AVCodecContext* codecContext = avcodec_alloc_context3(codec);
    if (!codecContext) {
        std::cerr << "Failed to allocate codec context" << std::endl;
        avformat_close_input(&formatContext);
        avformat_free_context(formatContext);
        return false;
    }

    if (avcodec_parameters_to_context(codecContext, codecParameters) != 0) {
        std::cerr << "Error: Could not copy codec parameters to codec context." << std::endl;
        avformat_close_input(&formatContext);
        avformat_free_context(formatContext);
        avcodec_free_context(&codecContext);
        return false;
    }

    if (avcodec_open2(codecContext, codec, nullptr) < 0) {
        std::cerr << "Error: Could not open audio decoder." << std::endl;
        avformat_close_input(&formatContext);
        avformat_free_context(formatContext);
        avcodec_free_context(&codecContext);
        return false;
    }

    // 设置重采样器 - 按照官方逻辑
    SwrContext* swr_ctx = swr_alloc_set_opts(
        nullptr, // allocate a new context
        AV_CH_LAYOUT_MONO, // output channel layout (mono)
        AV_SAMPLE_FMT_S16, // output sample format (signed 16-bit)
        dest_sample_rate, // output sample rate
        av_get_default_channel_layout(codecContext->channels), // input channel layout
        codecContext->sample_fmt, // input sample format
        codecContext->sample_rate, // input sample rate
        0, // logging level
        nullptr // parent context
    );

    if (swr_ctx == nullptr) {
        std::cerr << "Could not initialize resampler" << std::endl;
        avformat_close_input(&formatContext);
        avformat_free_context(formatContext);
        avcodec_free_context(&codecContext);
        return false;
    }

    if (swr_init(swr_ctx) != 0) {
        std::cerr << "Could not initialize resampler" << std::endl;
        avformat_close_input(&formatContext);
        avformat_free_context(formatContext);
        avcodec_free_context(&codecContext);
        swr_free(&swr_ctx);
        return false;
    }

    // 读取和解码音频数据 - 按照官方逻辑
    AVPacket* packet = av_packet_alloc();
    AVFrame* frame = av_frame_alloc();
    std::vector<uint8_t> resampled_buffers;

    while (av_read_frame(formatContext, packet) >= 0) {
        if (packet->stream_index == audioStreamIndex) {
            if (avcodec_send_packet(codecContext, packet) >= 0) {
                while (avcodec_receive_frame(codecContext, frame) >= 0) {
                    // Resample audio if necessary
                    std::vector<uint8_t> resampled_buffer;
                    int out_samples = av_rescale_rnd(swr_get_delay(swr_ctx, codecContext->sample_rate) + frame->nb_samples,
                                                    dest_sample_rate,
                                                    codecContext->sample_rate,
                                                    AV_ROUND_DOWN);

                    int resampled_size = out_samples * av_get_bytes_per_sample(AV_SAMPLE_FMT_S16);
                    if (resampled_buffer.size() < resampled_size) {
                        resampled_buffer.resize(resampled_size);
                    }
                    uint8_t* resampled_data = resampled_buffer.data();
                    int ret = swr_convert(
                        swr_ctx,
                        &resampled_data, // output buffer
                        out_samples, // output buffer size
                        (const uint8_t**)(frame->data), // input data
                        frame->nb_samples // input buffer size
                    );
                    if (ret < 0) {
                        std::cerr << "Error resampling audio" << std::endl;
                        break;
                    }
                    resampled_buffers.insert(resampled_buffers.end(), resampled_buffer.begin(), resampled_buffer.begin() + resampled_size);
                }
            }
        }
        av_packet_unref(packet);
    }

    // 清理FFmpeg资源
    avformat_close_input(&formatContext);
    avformat_free_context(formatContext);
    avcodec_free_context(&codecContext);
    swr_free(&swr_ctx);
    av_packet_free(&packet);
    av_frame_free(&frame);

    // 转换为float数组 - 按照官方逻辑
    speech_len = (resampled_buffers.size()) / 2;
    speech_data = (float*)malloc(sizeof(float) * speech_len);
    if (speech_data) {
        memset(speech_data, 0, sizeof(float) * speech_len);
        float scale = 1;
        if (data_type == 1) {
            scale = 32768.0f;  // 官方归一化逻辑
        }
        for (int32_t i = 0; i < speech_len; ++i) {
            int16_t val = (int16_t)((resampled_buffers[2 * i + 1] << 8) | resampled_buffers[2 * i]);
            speech_data[i] = (float)val / scale;
        }

        std::cout << "FunASR FFmpeg加载成功: " << speech_len << " 采样点, "
                  << (float)speech_len / dest_sample_rate << " 秒" << std::endl;
        return true;
    } else {
        return false;
    }

#endif
}

void AudioUtils::SaveAudio(const std::string& file_path, const std::vector<float>& audio_data, int sample_rate) {
    SF_INFO sf_info;
    memset(&sf_info, 0, sizeof(sf_info));
    sf_info.samplerate = sample_rate;
    sf_info.channels = 1;
    sf_info.format = SF_FORMAT_WAV | SF_FORMAT_FLOAT;
    
    SNDFILE* file = sf_open(file_path.c_str(), SFM_WRITE, &sf_info);
    if (!file) {
        std::cerr << "Error: Cannot create audio file: " << file_path << std::endl;
        return;
    }
    
    sf_count_t frames_written = sf_writef_float(file, audio_data.data(), audio_data.size());
    sf_close(file);
    
    if (frames_written != static_cast<sf_count_t>(audio_data.size())) {
        std::cerr << "Warning: Expected to write " << audio_data.size() 
                  << " frames, but wrote " << frames_written << std::endl;
    }
}

std::vector<float> AudioUtils::Resample(const std::vector<float>& audio, int orig_sr, int target_sr) {
    if (orig_sr == target_sr) {
        return audio;
    }
    
    // Simple linear interpolation resampling
    // For production use, consider using a proper resampling library like libsamplerate
    double ratio = static_cast<double>(target_sr) / orig_sr;
    size_t new_length = static_cast<size_t>(audio.size() * ratio);
    std::vector<float> resampled(new_length);
    
    for (size_t i = 0; i < new_length; ++i) {
        double src_index = i / ratio;
        size_t index = static_cast<size_t>(src_index);
        double frac = src_index - index;
        
        if (index + 1 < audio.size()) {
            resampled[i] = audio[index] * (1.0 - frac) + audio[index + 1] * frac;
        } else if (index < audio.size()) {
            resampled[i] = audio[index];
        } else {
            resampled[i] = 0.0f;
        }
    }
    
    return resampled;
}

// ConfigUtils implementation
VADConfig ConfigUtils::LoadVADConfig(const std::string& config_file) {
    VADConfig config;
    
    try {
        YAML::Node yaml_config = YAML::LoadFile(config_file);
        
        if (yaml_config["model_conf"]) {
            auto model_conf = yaml_config["model_conf"];
            if (model_conf["max_end_silence_time"]) {
                config.max_end_silence_time = model_conf["max_end_silence_time"].as<int>();
            }
            if (model_conf["max_single_segment_time"]) {
                config.max_single_segment_time = model_conf["max_single_segment_time"].as<int>();
            }
            if (model_conf["speech_noise_thres"]) {
                config.speech_noise_thres = model_conf["speech_noise_thres"].as<float>();
            }
        }
        
        if (yaml_config["frontend_conf"]) {
            auto frontend_conf = yaml_config["frontend_conf"];
            if (frontend_conf["lfr_m"]) {
                config.lfr_m = frontend_conf["lfr_m"].as<int>();
            }
            if (frontend_conf["lfr_n"]) {
                config.lfr_n = frontend_conf["lfr_n"].as<int>();
            }
        }
        
        if (yaml_config["encoder_conf"]) {
            auto encoder_conf = yaml_config["encoder_conf"];
            if (encoder_conf["fsmn_layers"]) {
                config.fsmn_layers = encoder_conf["fsmn_layers"].as<int>();
            }
            if (encoder_conf["proj_dim"]) {
                config.proj_dim = encoder_conf["proj_dim"].as<int>();
            }
            if (encoder_conf["lorder"]) {
                config.lorder = encoder_conf["lorder"].as<int>();
            }
        }
        
    } catch (const std::exception& e) {
        std::cerr << "Error loading VAD config: " << e.what() << std::endl;
        std::cerr << "Using default configuration" << std::endl;
    }
    
    return config;
}

ASRConfig ConfigUtils::LoadASRConfig(const std::string& config_file) {
    ASRConfig config;
    
    try {
        YAML::Node yaml_config = YAML::LoadFile(config_file);
        
        if (yaml_config["frontend_conf"]) {
            auto frontend_conf = yaml_config["frontend_conf"];
            if (frontend_conf["lfr_m"]) {
                config.lfr_m = frontend_conf["lfr_m"].as<int>();
            }
            if (frontend_conf["lfr_n"]) {
                config.lfr_n = frontend_conf["lfr_n"].as<int>();
            }
            if (frontend_conf["n_mels"]) {
                config.n_mels = frontend_conf["n_mels"].as<int>();
            }
        }
        
    } catch (const std::exception& e) {
        std::cerr << "Error loading ASR config: " << e.what() << std::endl;
        std::cerr << "Using default configuration" << std::endl;
    }
    
    return config;
}

std::vector<float> ConfigUtils::LoadCMVN(const std::string& cmvn_file) {
    std::vector<float> cmvn_data;

    std::ifstream file(cmvn_file);
    if (!file.is_open()) {
        std::cerr << "Error: Cannot open CMVN file: " << cmvn_file << std::endl;
        return cmvn_data;
    }

    std::string line;
    bool in_addshift = false;
    bool in_rescale = false;

    while (std::getline(file, line)) {
        // Look for AddShift section (means)
        if (line.find("<AddShift>") != std::string::npos) {
            in_addshift = true;
            continue;
        }
        // Look for Rescale section (vars/stds)
        else if (line.find("<Rescale>") != std::string::npos) {
            in_rescale = true;
            continue;
        }
        // End of sections
        else if (line.find("</Nnet>") != std::string::npos) {
            break;
        }

        // Parse data lines that start with <LearnRateCoef>
        if ((in_addshift || in_rescale) && line.find("<LearnRateCoef>") != std::string::npos) {
            // Find the [ and ] brackets
            size_t start = line.find('[');
            size_t end = line.find(']');

            if (start != std::string::npos && end != std::string::npos && end > start) {
                std::string data_str = line.substr(start + 1, end - start - 1);

                // Parse the numbers
                std::istringstream iss(data_str);
                std::string token;
                while (iss >> token) {
                    try {
                        float value = std::stof(token);
                        cmvn_data.push_back(value);
                    } catch (const std::exception& e) {
                        // Skip invalid tokens
                    }
                }
            }
        }
    }

    file.close();

    std::cout << "CMVN加载完成: " << cmvn_data.size() << " 个参数" << std::endl;
    if (!cmvn_data.empty()) {
        std::cout << "前几个值: ";
        for (size_t i = 0; i < std::min(cmvn_data.size(), size_t(5)); ++i) {
            std::cout << cmvn_data[i] << " ";
        }
        std::cout << std::endl;
    }

    return cmvn_data;
}

} // namespace funasr_openvino
