#include "../include/sensevoice_openvino.h"
#include <filesystem>
#include <fstream>

namespace funasr_openvino {

// SentencePieceTokenizer implementation
SentencePieceTokenizer::SentencePieceTokenizer(const std::string& model_path)
    : model_path_(model_path), is_loaded_(false) {
    LoadModel();
}

void SentencePieceTokenizer::LoadModel() {
    // In a real implementation, you would use the sentencepiece library
    // For now, this is a placeholder
    if (!std::filesystem::exists(model_path_)) {
        std::cerr << "Error: SentencePiece model not found: " << model_path_ << std::endl;
        return;
    }

    auto status = processor_.Load(model_path_);
    if (!status.ok()) {
        std::cerr << "Error: Failed to load SentencePiece model: " << status.ToString() << std::endl;
        return;
    }

    is_loaded_ = true;
    std::cout << "✓ SentencePiece模型加载成功: " << model_path_ << std::endl;
    std::cout << "  词汇表大小: " << processor_.GetPieceSize() << std::endl;
}

std::string SentencePieceTokenizer::Decode(const std::vector<int>& token_ids) {
    if (!is_loaded_) {
        std::cerr << "Error: SentencePiece model not loaded" << std::endl;
        return "";
    }

    std::string result;
    auto status = processor_.Decode(token_ids, &result);
    if (!status.ok()) {
        std::cerr << "Error: Failed to decode tokens: " << status.ToString() << std::endl;
        return "";
    }

    return result;
}

std::vector<int> SentencePieceTokenizer::Encode(const std::string& text) {
    if (!is_loaded_) {
        std::cerr << "Error: SentencePiece model not loaded" << std::endl;
        return {};
    }

    std::vector<int> tokens;
    auto status = processor_.Encode(text, &tokens);
    if (!status.ok()) {
        std::cerr << "Error: Failed to encode text: " << status.ToString() << std::endl;
        return {};
    }

    return tokens;
}

// SenseVoiceWavFrontend implementation
SenseVoiceWavFrontend::SenseVoiceWavFrontend(const std::string& cmvn_file, int lfr_m, int lfr_n, int n_mels)
    : lfr_m_(lfr_m), lfr_n_(lfr_n), n_mels_(n_mels) {
    LoadCMVN(cmvn_file);
}

void SenseVoiceWavFrontend::LoadCMVN(const std::string& cmvn_file) {
    auto cmvn_data = ConfigUtils::LoadCMVN(cmvn_file);
    
    if (cmvn_data.empty()) {
        std::cerr << "Warning: CMVN file is empty or cannot be loaded" << std::endl;
        return;
    }
    
    // Assuming CMVN data format: [means..., vars...]
    size_t half_size = cmvn_data.size() / 2;
    means_list_.assign(cmvn_data.begin(), cmvn_data.begin() + half_size);
    vars_list_.assign(cmvn_data.begin() + half_size, cmvn_data.end());
}

std::pair<std::vector<std::vector<float>>, int> SenseVoiceWavFrontend::Fbank(const std::vector<float>& waveform) {
    auto fbank_features = ExtractFbank(waveform);
    return {fbank_features, static_cast<int>(fbank_features.size())};
}

std::pair<std::vector<std::vector<float>>, int> SenseVoiceWavFrontend::LfrCmvn(const std::vector<std::vector<float>>& speech) {
    auto lfr_features = ApplyLFR(speech);
    ApplyCMVN(lfr_features);
    return {lfr_features, static_cast<int>(lfr_features.size())};
}

std::vector<std::vector<float>> SenseVoiceWavFrontend::ExtractFbank(const std::vector<float>& waveform) {
    if (waveform.empty()) {
        return {};
    }

    // Check for invalid values in input
    bool has_nan = false, has_inf = false;
    for (float val : waveform) {
        if (std::isnan(val)) has_nan = true;
        if (std::isinf(val)) has_inf = true;
    }
    if (has_nan || has_inf) {
        std::cerr << "警告: 输入音频包含无效值 (NaN=" << has_nan << ", Inf=" << has_inf << ")" << std::endl;
        return {};
    }

    int frame_length = 400;  // 25ms at 16kHz
    int frame_shift = 160;   // 10ms at 16kHz

    std::vector<std::vector<float>> features;

    // Calculate number of frames
    int num_frames = (waveform.size() - frame_length) / frame_shift + 1;
    if (num_frames <= 0) {
        num_frames = 1;
    }

    for (int i = 0; i < num_frames; ++i) {
        int start_idx = i * frame_shift;
        int end_idx = start_idx + frame_length;

        std::vector<float> frame(frame_length, 0.0f);

        // Copy audio data with padding if necessary
        for (int j = 0; j < frame_length; ++j) {
            int audio_idx = start_idx + j;
            if (audio_idx < static_cast<int>(waveform.size())) {
                frame[j] = waveform[audio_idx];
            }
        }

        // Apply Hamming window
        for (int j = 0; j < frame_length; ++j) {
            float window = 0.54f - 0.46f * std::cos(2.0f * M_PI * j / (frame_length - 1));
            frame[j] *= window;
        }

        // Extract mel-scale features (improved version)
        std::vector<float> mel_features(n_mels_, 0.0f);

        // Simplified mel filterbank - distribute energy across mel bins
        for (int k = 0; k < n_mels_; ++k) {
            float energy = 0.0f;
            int start_bin = k * frame_length / n_mels_;
            int end_bin = (k + 1) * frame_length / n_mels_;

            for (int j = start_bin; j < end_bin && j < frame_length; ++j) {
                energy += frame[j] * frame[j];
            }

            // Normalize by bin width and add small epsilon
            energy = energy / (end_bin - start_bin + 1e-8f);
            mel_features[k] = std::log(energy + 1e-8f);

            // Check for invalid values
            if (std::isnan(mel_features[k]) || std::isinf(mel_features[k])) {
                mel_features[k] = -10.0f;  // Set to a reasonable log value
            }
        }

        features.push_back(mel_features);
    }

    return features;
}

std::vector<std::vector<float>> SenseVoiceWavFrontend::ApplyLFR(const std::vector<std::vector<float>>& features) {
    if (features.empty()) return {};
    
    std::vector<std::vector<float>> lfr_features;
    
    for (size_t i = 0; i < features.size(); i += lfr_n_) {
        std::vector<float> lfr_frame;
        
        for (int j = 0; j < lfr_m_; ++j) {
            int frame_idx = static_cast<int>(i) + j - lfr_m_ / 2;
            if (frame_idx < 0) frame_idx = 0;
            if (frame_idx >= static_cast<int>(features.size())) frame_idx = features.size() - 1;
            
            const auto& frame = features[frame_idx];
            lfr_frame.insert(lfr_frame.end(), frame.begin(), frame.end());
        }
        
        lfr_features.push_back(lfr_frame);
    }
    
    return lfr_features;
}

void SenseVoiceWavFrontend::ApplyCMVN(std::vector<std::vector<float>>& features) {
    if (features.empty() || means_list_.empty() || vars_list_.empty()) {
        return;
    }

    // Ensure means and vars have the same size
    size_t min_size = std::min(means_list_.size(), vars_list_.size());

    for (auto& frame : features) {
        for (size_t i = 0; i < frame.size() && i < min_size; ++i) {
            // Apply CMVN: (x - mean) / std, but vars_list_ might be 1/std
            float normalized = (frame[i] - means_list_[i]) * vars_list_[i];

            // Only check for invalid values, don't clamp normal values
            if (std::isnan(normalized) || std::isinf(normalized)) {
                normalized = 0.0f;
            }

            frame[i] = normalized;
        }
    }
}

// SenseVoiceOpenVINO implementation
SenseVoiceOpenVINO::SenseVoiceOpenVINO(const std::string& model_path, const std::string& device)
    : model_path_(model_path), device_(device) {
    
    // Initialize language and text normalization mappings
    lid_dict_ = {
        {"auto", 0}, {"zh", 3}, {"en", 4}, {"yue", 7}, 
        {"ja", 11}, {"ko", 12}, {"nospeech", 13}
    };
    textnorm_dict_ = {{"withitn", 14}, {"woitn", 15}};
    
    LoadModel();
    LoadFrontendAndTokenizer();
    
    std::cout << "✓ SenseVoice OpenVINO模型加载成功" << std::endl;
    std::cout << "  模型路径: " << model_path << std::endl;
    std::cout << "  推理设备: " << device << std::endl;
}

void SenseVoiceOpenVINO::LoadModel() {
    if (!std::filesystem::exists(model_path_)) {
        throw std::runtime_error("OpenVINO模型不存在: " + model_path_);
    }
    
    model_ = core_.read_model(model_path_);
    compiled_model_ = core_.compile_model(model_, device_);
}

void SenseVoiceOpenVINO::LoadFrontendAndTokenizer() {
    std::string model_dir = std::filesystem::path(model_path_).parent_path().string();
    
    // Load frontend
    std::string config_file = model_dir + "/config.yaml";
    std::string cmvn_file = model_dir + "/am.mvn";
    
    if (std::filesystem::exists(config_file)) {
        try {
            auto config = ConfigUtils::LoadASRConfig(config_file);
            frontend_ = std::make_unique<SenseVoiceWavFrontend>(cmvn_file, config.lfr_m, config.lfr_n, config.n_mels);
            std::cout << "✓ 前端处理器加载成功" << std::endl;
        } catch (const std::exception& e) {
            std::cerr << "⚠️ 前端处理器加载失败: " << e.what() << std::endl;
        }
    }
    
    // Load tokenizer
    std::string bpe_model_path = model_dir + "/chn_jpn_yue_eng_ko_spectok.bpe.model";
    if (std::filesystem::exists(bpe_model_path)) {
        try {
            tokenizer_ = std::make_unique<SentencePieceTokenizer>(bpe_model_path);
            std::cout << "✓ 分词器加载成功" << std::endl;
        } catch (const std::exception& e) {
            std::cerr << "⚠️ 分词器加载失败: " << e.what() << std::endl;
        }
    }
}

std::string SenseVoiceOpenVINO::InferenceAudioFile(const std::string& audio_path, const std::string& language) {
    if (!frontend_) {
        throw std::runtime_error("前端处理器未加载");
    }
    
    // Language mapping
    int language_id = 3;  // Default to Chinese (zh)
    if (lid_dict_.find(language) != lid_dict_.end()) {
        language_id = lid_dict_[language];
    }
    std::cout << "语言设置: " << language << " -> ID=" << language_id << std::endl;
    int textnorm_id = textnorm_dict_["withitn"];
    
    try {
        // Extract features
        auto [feats, feats_len] = ExtractFeatures(audio_path);

        // std::cout << "ASR特征提取完成: " << feats.size() << " 帧, "
        //           << (feats.empty() ? 0 : feats[0].size()) << " 维, 长度=" << feats_len[0] << std::endl;

        if (feats.empty() || feats_len[0] == 0) {
            std::cerr << "错误: 特征提取失败或音频为空" << std::endl;
            return "";
        }

        // Create properly sized tensors for SenseVoice model
        auto infer_request = compiled_model_.create_infer_request();

        // Get input shapes and create tensors
        auto speech_port = compiled_model_.input(0);  // speech
        auto lengths_port = compiled_model_.input(1); // speech_lengths
        auto language_port = compiled_model_.input(2); // language
        auto textnorm_port = compiled_model_.input(3); // textnorm

        // Create speech tensor with actual shape [1, seq_len, feature_dim]
        ov::Shape speech_shape = {1, static_cast<size_t>(feats.size()), static_cast<size_t>(feats[0].size())};
        ov::Tensor speech_tensor(speech_port.get_element_type(), speech_shape);
        float* speech_data = speech_tensor.data<float>();

        size_t offset = 0;
        for (const auto& frame : feats) {
            for (float val : frame) {
                speech_data[offset++] = val;
            }
        }

        // Create lengths tensor [1]
        ov::Shape lengths_shape = {1};
        ov::Tensor lengths_tensor(lengths_port.get_element_type(), lengths_shape);
        int* lengths_data = lengths_tensor.data<int>();
        lengths_data[0] = feats_len[0];

        // Create language tensor [1]
        ov::Shape language_shape = {1};
        ov::Tensor language_tensor(language_port.get_element_type(), language_shape);
        int* language_data = language_tensor.data<int>();
        language_data[0] = language_id;

        // Create textnorm tensor [1]
        ov::Shape textnorm_shape = {1};
        ov::Tensor textnorm_tensor(textnorm_port.get_element_type(), textnorm_shape);
        int* textnorm_data = textnorm_tensor.data<int>();
        textnorm_data[0] = textnorm_id;

        // Set all input tensors
        infer_request.set_input_tensor(0, speech_tensor);
        infer_request.set_input_tensor(1, lengths_tensor);
        infer_request.set_input_tensor(2, language_tensor);
        infer_request.set_input_tensor(3, textnorm_tensor);
        
        // Run inference
        std::cout << "开始ASR推理..." << std::endl;
        infer_request.infer();
        std::cout << "ASR推理完成" << std::endl;

        // Get outputs
        auto output_tensor = infer_request.get_output_tensor(0);
        auto lengths_output = infer_request.get_output_tensor(1);

        auto output_shape = output_tensor.get_shape();
        std::cout << "输出张量形状: [";
        for (size_t i = 0; i < output_shape.size(); ++i) {
            std::cout << output_shape[i];
            if (i < output_shape.size() - 1) std::cout << ", ";
        }
        std::cout << "]" << std::endl;

        float* logits = output_tensor.data<float>();
        int* output_lengths = lengths_output.data<int>();

        std::cout << "输出长度: " << output_lengths[0] << std::endl;

        // Post-process
        auto result = PostProcess(std::vector<float>(logits, logits + output_tensor.get_size()),
                                 output_lengths[0]);

        std::cout << "后处理结果: '" << result << "'" << std::endl;
        return result;
        
    } catch (const std::exception& e) {
        std::cerr << "⚠️ 推理失败: " << e.what() << std::endl;
        return "";
    }
}

std::pair<std::vector<std::vector<float>>, std::vector<int>> SenseVoiceOpenVINO::ExtractFeatures(
    const std::string& audio_path) {

    // Load audio
    auto waveform = AudioUtils::LoadAudio(audio_path, 16000);
    std::cout << "ExtractFeatures: 音频长度=" << waveform.size() << " 采样点, "
              << (float)waveform.size() / 16000.0f << " 秒" << std::endl;

    // Extract features
    auto [speech, _] = frontend_->Fbank(waveform);
    std::cout << "Fbank特征: " << speech.size() << " 帧" << std::endl;

    auto [feat, feat_len] = frontend_->LfrCmvn(speech);
    std::cout << "LFR+CMVN特征: " << feat.size() << " 帧, 长度=" << feat_len << std::endl;

    // Check features for invalid values
    bool has_nan = false, has_inf = false;
    float min_val = 0.0f, max_val = 0.0f;
    if (!feat.empty() && !feat[0].empty()) {
        min_val = max_val = feat[0][0];
        for (const auto& frame : feat) {
            for (float val : frame) {
                if (std::isnan(val)) has_nan = true;
                if (std::isinf(val)) has_inf = true;
                min_val = std::min(min_val, val);
                max_val = std::max(max_val, val);
            }
        }
    }

    std::cout << "特征检查: NaN=" << has_nan << ", Inf=" << has_inf
              << ", 范围=[" << min_val << ", " << max_val << "]" << std::endl;

    if (has_nan || has_inf) {
        std::cerr << "错误: 特征包含无效值，清理中..." << std::endl;
        // Clean invalid values
        for (auto& frame : feat) {
            for (float& val : frame) {
                if (std::isnan(val) || std::isinf(val)) {
                    val = 0.0f;
                }
            }
        }
    }

    return {feat, {feat_len}};
}

std::pair<std::vector<std::vector<std::vector<float>>>, std::vector<int>> SenseVoiceOpenVINO::ExtractFeaturesBatch(
    const std::vector<std::vector<float>>& audio_segments) {

    std::vector<std::vector<std::vector<float>>> feats_list;
    std::vector<int> feats_len_list;

    for (const auto& audio_segment : audio_segments) {
        auto [speech, _] = frontend_->Fbank(audio_segment);
        auto [feat, feat_len] = frontend_->LfrCmvn(speech);
        feats_list.push_back(feat);
        feats_len_list.push_back(feat_len);
    }

    // Pad to same length
    int max_len = *std::max_element(feats_len_list.begin(), feats_len_list.end());
    for (auto& feats : feats_list) {
        while (static_cast<int>(feats.size()) < max_len) {
            if (!feats.empty()) {
                std::vector<float> zero_frame(feats[0].size(), 0.0f);
                feats.push_back(zero_frame);
            }
        }
    }

    return {feats_list, feats_len_list};
}

std::string SenseVoiceOpenVINO::PostProcess(const std::vector<float>& logits, int length) {
    std::cout << "开始后处理，logits大小: " << logits.size() << ", 长度: " << length << std::endl;

    // CTC decoding
    auto tokens = CTCDecode(logits, length);
    std::cout << "CTC解码得到 " << tokens.size() << " 个token" << std::endl;

    // Print first few tokens for debugging
    if (!tokens.empty()) {
        std::cout << "前几个token: ";
        for (size_t i = 0; i < std::min(tokens.size(), size_t(10)); ++i) {
            std::cout << tokens[i] << " ";
        }
        std::cout << std::endl;
    }

    // Remove duplicates and blank tokens
    tokens = RemoveDuplicates(tokens);
    std::cout << "去重后: " << tokens.size() << " 个token" << std::endl;
    if (!tokens.empty()) {
        std::cout << "去重后token: ";
        for (size_t i = 0; i < std::min(tokens.size(), size_t(10)); ++i) {
            std::cout << tokens[i] << " ";
        }
        std::cout << std::endl;
    }

    tokens = RemoveBlankTokens(tokens, 0);
    std::cout << "去除空白后: " << tokens.size() << " 个token" << std::endl;
    if (!tokens.empty()) {
        std::cout << "最终token: ";
        for (size_t i = 0; i < tokens.size(); ++i) {
            std::cout << tokens[i] << " ";
        }
        std::cout << std::endl;
    }

    // Convert to text
    if (tokenizer_ && !tokens.empty()) {
        auto result = tokenizer_->Decode(tokens);
        std::cout << "分词器解码结果: '" << result << "'" << std::endl;
        return result;
    } else if (!tokens.empty()) {
        // Fallback: convert tokens to string directly
        std::string result;
        for (int token : tokens) {
            result += std::to_string(token) + " ";
        }
        std::cout << "直接转换结果: '" << result << "'" << std::endl;
        return result;
    }

    std::cout << "无token或分词器未加载" << std::endl;
    return "";
}

std::vector<int> SenseVoiceOpenVINO::CTCDecode(const std::vector<float>& logits, int length) {
    std::vector<int> tokens;

    // Assuming logits are in shape [time, vocab_size]
    size_t vocab_size = logits.size() / length;
    std::cout << "CTC解码: 时间步=" << length << ", 词汇表大小=" << vocab_size << std::endl;

    // Check for invalid values in logits
    bool has_nan = false, has_inf = false;
    float min_val = logits[0], max_val = logits[0];
    for (size_t i = 0; i < std::min(logits.size(), size_t(1000)); ++i) {
        if (std::isnan(logits[i])) has_nan = true;
        if (std::isinf(logits[i])) has_inf = true;
        min_val = std::min(min_val, logits[i]);
        max_val = std::max(max_val, logits[i]);
    }
    std::cout << "Logits检查: NaN=" << has_nan << ", Inf=" << has_inf
              << ", 范围=[" << min_val << ", " << max_val << "]" << std::endl;

    for (int t = 0; t < length; ++t) {
        // Apply softmax to get probabilities
        std::vector<float> probs(vocab_size);
        float max_logit = logits[t * vocab_size];
        for (size_t v = 1; v < vocab_size; ++v) {
            max_logit = std::max(max_logit, logits[t * vocab_size + v]);
        }

        float sum_exp = 0.0f;
        for (size_t v = 0; v < vocab_size; ++v) {
            probs[v] = std::exp(logits[t * vocab_size + v] - max_logit);
            sum_exp += probs[v];
        }

        for (size_t v = 0; v < vocab_size; ++v) {
            probs[v] /= sum_exp;
        }

        // Find best token
        int best_token = 0;
        float best_prob = probs[0];

        for (size_t v = 1; v < vocab_size; ++v) {
            if (probs[v] > best_prob) {
                best_prob = probs[v];
                best_token = static_cast<int>(v);
            }
        }

        // Debug: print top 5 tokens for first few time steps and some middle steps
        if (t < 5 || (t >= 10 && t < 15) || (t >= 50 && t < 55)) {
            std::cout << "时间步 " << t << " 最佳token=" << best_token << " 概率=" << best_prob;

            // Find top 5 tokens
            std::vector<std::pair<float, int>> top_tokens;
            for (size_t v = 0; v < vocab_size; ++v) {
                top_tokens.push_back({probs[v], static_cast<int>(v)});
            }
            std::sort(top_tokens.rbegin(), top_tokens.rend());

            std::cout << " 前5: ";
            for (int i = 0; i < 5 && i < static_cast<int>(top_tokens.size()); ++i) {
                std::cout << "(" << top_tokens[i].second << ":" << top_tokens[i].first << ") ";
            }
            std::cout << std::endl;
        }

        tokens.push_back(best_token);
    }

    return tokens;
}

std::vector<int> SenseVoiceOpenVINO::RemoveDuplicates(const std::vector<int>& tokens) {
    std::vector<int> unique_tokens;
    int prev = -1;

    for (int token : tokens) {
        if (token != prev) {
            unique_tokens.push_back(token);
            prev = token;
        }
    }

    return unique_tokens;
}

std::vector<int> SenseVoiceOpenVINO::RemoveBlankTokens(const std::vector<int>& tokens, int blank_id) {
    std::vector<int> filtered_tokens;

    for (int token : tokens) {
        if (token != blank_id) {
            filtered_tokens.push_back(token);
        }
    }

    return filtered_tokens;
}

std::vector<std::string> SenseVoiceOpenVINO::InferenceBatchDirect(
    const std::vector<std::vector<float>>& audio_segments,
    const std::string& language) {

    if (audio_segments.empty()) {
        return {};
    }

    // Language mapping
    int language_id = 3;  // Default to Chinese
    if (lid_dict_.find(language) != lid_dict_.end()) {
        language_id = lid_dict_[language];
    }
    int textnorm_id = textnorm_dict_["withitn"];

    try {
        // Extract features for all segments
        auto [batch_feats, batch_feats_len] = ExtractFeaturesBatch(audio_segments);
        int batch_size = audio_segments.size();

        // Prepare inputs
        auto infer_request = compiled_model_.create_infer_request();

        // Set batch inputs (index 0)
        auto speech_tensor = infer_request.get_input_tensor(0);
        float* speech_data = speech_tensor.data<float>();
        size_t offset = 0;
        for (const auto& feats : batch_feats) {
            for (const auto& frame : feats) {
                for (float val : frame) {
                    speech_data[offset++] = val;
                }
            }
        }

        // Set lengths (index 1)
        auto lengths_tensor = infer_request.get_input_tensor(1);
        int* lengths_data = lengths_tensor.data<int>();
        for (int i = 0; i < batch_size; ++i) {
            lengths_data[i] = batch_feats_len[i];
        }

        // Set language and textnorm for all samples (index 2 and 3)
        auto language_tensor = infer_request.get_input_tensor(2);
        int* language_data = language_tensor.data<int>();
        auto textnorm_tensor = infer_request.get_input_tensor(3);
        int* textnorm_data = textnorm_tensor.data<int>();

        for (int i = 0; i < batch_size; ++i) {
            language_data[i] = language_id;
            textnorm_data[i] = textnorm_id;
        }

        // Run inference
        infer_request.infer();

        // Process outputs
        auto output_tensor = infer_request.get_output_tensor(0);
        float* logits = output_tensor.data<float>();

        std::vector<std::string> results;
        auto output_shape = output_tensor.get_shape();
        size_t seq_len = output_shape[1];
        size_t vocab_size = output_shape[2];

        for (int i = 0; i < batch_size; ++i) {
            size_t start_idx = i * seq_len * vocab_size;
            std::vector<float> sample_logits(logits + start_idx,
                                           logits + start_idx + seq_len * vocab_size);

            std::string result = PostProcess(sample_logits, batch_feats_len[i]);
            results.push_back(result);
        }

        return results;

    } catch (const std::exception& e) {
        std::cerr << "⚠️ 批量推理失败，降级到逐个处理: " << e.what() << std::endl;
        return InferenceBatchFallback(audio_segments, language);
    }
}

std::vector<std::string> SenseVoiceOpenVINO::InferenceBatchFallback(
    const std::vector<std::vector<float>>& audio_segments,
    const std::string& language) {

    std::vector<std::string> results;

    for (const auto& audio_segment : audio_segments) {
        try {
            // Save to temporary file and process
            std::string temp_file = "/tmp/temp_audio_" + std::to_string(rand()) + ".wav";
            AudioUtils::SaveAudio(temp_file, audio_segment, 16000);

            std::string result = InferenceAudioFile(temp_file, language);
            results.push_back(result);

            // Clean up
            std::filesystem::remove(temp_file);

        } catch (const std::exception& e) {
            std::cerr << "⚠️ 单个推理失败: " << e.what() << std::endl;
            results.push_back("");
        }
    }

    return results;
}

} // namespace funasr_openvino
