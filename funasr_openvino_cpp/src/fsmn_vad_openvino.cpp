#include "../include/fsmn_vad_openvino.h"
#include <filesystem>

namespace funasr_openvino {

// WavFrontend implementation
WavFrontend::WavFrontend(const std::string& cmvn_file, int lfr_m, int lfr_n, int n_mels)
    : lfr_m_(lfr_m), lfr_n_(lfr_n), n_mels_(n_mels) {
    LoadCMVN(cmvn_file);
}

void WavFrontend::LoadCMVN(const std::string& cmvn_file) {
    auto cmvn_data = ConfigUtils::LoadCMVN(cmvn_file);
    
    if (cmvn_data.empty()) {
        std::cerr << "Warning: CMVN file is empty or cannot be loaded" << std::endl;
        return;
    }
    
    // Assuming CMVN data format: [means..., vars...]
    size_t half_size = cmvn_data.size() / 2;
    means_list_.assign(cmvn_data.begin(), cmvn_data.begin() + half_size);
    vars_list_.assign(cmvn_data.begin() + half_size, cmvn_data.end());
}

std::pair<std::vector<std::vector<float>>, int> WavFrontend::Fbank(const std::vector<float>& waveform) {
    // Extract fbank features
    auto fbank_features = ExtractFbank(waveform);
    return {fbank_features, static_cast<int>(fbank_features.size())};
}

std::pair<std::vector<std::vector<float>>, int> WavFrontend::LfrCmvn(const std::vector<std::vector<float>>& speech) {
    // Apply LFR (Linear Feature Reduction)
    auto lfr_features = ApplyLFR(speech);
    
    // Apply CMVN (Cepstral Mean and Variance Normalization)
    ApplyCMVN(lfr_features);
    
    return {lfr_features, static_cast<int>(lfr_features.size())};
}

std::vector<std::vector<float>> WavFrontend::ExtractFbank(const std::vector<float>& waveform) {
    // Extract fbank features according to VAD config
    // frame_length: 25ms, frame_shift: 10ms, n_mels: 80

    if (waveform.empty()) {
        return {};
    }

    int frame_length = 400;  // 25ms at 16kHz
    int frame_shift = 160;   // 10ms at 16kHz

    std::vector<std::vector<float>> features;

    // Calculate number of frames
    int num_frames = (waveform.size() - frame_length) / frame_shift + 1;
    if (num_frames <= 0) {
        num_frames = 1;
    }

    for (int i = 0; i < num_frames; ++i) {
        int start_idx = i * frame_shift;
        int end_idx = start_idx + frame_length;

        std::vector<float> frame(frame_length, 0.0f);

        // Copy audio data with padding if necessary
        for (int j = 0; j < frame_length; ++j) {
            int audio_idx = start_idx + j;
            if (audio_idx < static_cast<int>(waveform.size())) {
                frame[j] = waveform[audio_idx];
            }
        }

        // Apply Hamming window
        for (int j = 0; j < frame_length; ++j) {
            float window = 0.54f - 0.46f * std::cos(2.0f * M_PI * j / (frame_length - 1));
            frame[j] *= window;
        }

        // Simple mel-scale filterbank (80 dimensions)
        std::vector<float> mel_features(n_mels_, 0.0f);

        // Simplified mel filterbank - distribute energy across mel bins
        for (int k = 0; k < n_mels_; ++k) {
            float energy = 0.0f;
            int start_bin = k * frame_length / n_mels_;
            int end_bin = (k + 1) * frame_length / n_mels_;

            for (int j = start_bin; j < end_bin && j < frame_length; ++j) {
                energy += frame[j] * frame[j];
            }

            mel_features[k] = std::log(energy + 1e-8f);
        }

        features.push_back(mel_features);
    }

    return features;
}

std::vector<std::vector<float>> WavFrontend::ApplyLFR(const std::vector<std::vector<float>>& features) {
    if (features.empty()) return {};

    std::vector<std::vector<float>> lfr_features;
    int feature_dim = features[0].size();

    // VAD config: lfr_m=5, lfr_n=1
    // This means we concatenate 5 consecutive frames with stride 1

    // Ensure we have valid parameters
    if (lfr_m_ <= 0 || lfr_n_ <= 0) {
        return features;  // Return original features if parameters are invalid
    }

    // For VAD: lfr_m=5, lfr_n=1, so we process every frame
    for (size_t i = 0; i < features.size(); i += lfr_n_) {
        std::vector<float> lfr_frame;
        lfr_frame.reserve(feature_dim * lfr_m_);  // Reserve space: 80 * 5 = 400 dims

        // Concatenate lfr_m frames centered around current frame
        for (int j = 0; j < lfr_m_; ++j) {
            int frame_idx = static_cast<int>(i) + j - lfr_m_ / 2;  // Center around current frame

            // Boundary handling - repeat edge frames
            if (frame_idx < 0) {
                frame_idx = 0;
            } else if (frame_idx >= static_cast<int>(features.size())) {
                frame_idx = features.size() - 1;
            }

            const auto& frame = features[frame_idx];
            lfr_frame.insert(lfr_frame.end(), frame.begin(), frame.end());
        }

        lfr_features.push_back(lfr_frame);
    }

    return lfr_features;
}

void WavFrontend::ApplyCMVN(std::vector<std::vector<float>>& features) {
    if (features.empty() || means_list_.empty() || vars_list_.empty()) {
        return;
    }

    // Ensure means and vars have the same size
    size_t min_size = std::min(means_list_.size(), vars_list_.size());

    for (auto& frame : features) {
        for (size_t i = 0; i < frame.size() && i < min_size; ++i) {
            // Apply CMVN: (x - mean) / std
            frame[i] = (frame[i] - means_list_[i]) * vars_list_[i];
        }
    }
}

// E2EVadModel implementation
E2EVadModel::E2EVadModel(const VADConfig& config) : config_(config), is_final_(false) {}

std::vector<std::vector<std::vector<int>>> E2EVadModel::operator()(
    const std::vector<std::vector<std::vector<float>>>& scores,
    const std::vector<std::vector<float>>& waveform_batch,
    bool is_final,
    int max_end_sil,
    bool online) {
    
    std::vector<std::vector<std::vector<int>>> batch_segments;
    
    for (size_t i = 0; i < scores.size() && i < waveform_batch.size(); ++i) {
        auto segments = PostProcess(scores[i], waveform_batch[i], is_final, max_end_sil);
        batch_segments.push_back(segments);
    }
    
    return batch_segments;
}

std::vector<std::vector<int>> E2EVadModel::PostProcess(
    const std::vector<std::vector<float>>& scores,
    const std::vector<float>& waveform,
    bool is_final,
    int max_end_sil) {

    std::vector<std::vector<int>> segments;

    std::cout << "VAD后处理: scores大小=" << scores.size()
              << ", waveform大小=" << waveform.size() << std::endl;

    if (scores.empty() || waveform.empty()) {
        std::cout << "VAD后处理: 输入为空，返回空结果" << std::endl;
        return segments;
    }

    if (!scores.empty()) {
        std::cout << "第一帧scores维度: " << scores[0].size() << std::endl;
    }

    // 按照FunASR官方逻辑实现VAD后处理
    float threshold = config_.speech_noise_thres;
    float speech_2_noise_ratio = 1.0f;  // 官方默认值
    float snr_thres = -100.0f;          // 官方默认值
    float decibel_thres = -100.0f;      // 官方默认值
    float noise_average_decibel = -100.0f;

    std::cout << "使用FunASR官方VAD逻辑，阈值: " << threshold << std::endl;

    // 计算每帧的分贝值
    std::vector<float> decibel_values;
    int frame_shift = 160;  // 10ms
    int frame_length = 400; // 25ms

    for (size_t i = 0; i < scores.size(); ++i) {
        int start_sample = i * frame_shift;
        int end_sample = std::min(start_sample + frame_length, static_cast<int>(waveform.size()));

        float sum = 0.0f;
        for (int j = start_sample; j < end_sample; ++j) {
            sum += waveform[j] * waveform[j];
        }
        float decibel = 10.0f * std::log10(sum + 0.000001f);
        decibel_values.push_back(decibel);
    }

    // 状态机变量
    enum FrameState { SILENCE, SPEECH, INVALID };
    std::vector<FrameState> frame_states;

    for (size_t i = 0; i < scores.size(); ++i) {
        if (scores[i].empty()) {
            frame_states.push_back(INVALID);
            continue;
        }

        float cur_decibel = decibel_values[i];
        float cur_snr = cur_decibel - noise_average_decibel;

        // 如果分贝太低，直接判断为静音
        if (cur_decibel < decibel_thres) {
            frame_states.push_back(SILENCE);
            continue;
        }

        // 按照官方逻辑计算语音概率
        // VAD模型输出是softmax概率，不是log概率
        float sil_prob = scores[i][0];  // sil_pdf_ids = [0]，静音概率
        float speech_prob = 1.0f - sil_prob;  // 语音概率 = 1 - 静音概率

        // Debug: 打印原始scores值
        if (i < 3) {
            std::cout << "Frame " << i << " 原始scores前10维: ";
            for (size_t j = 0; j < std::min(scores[i].size(), size_t(10)); ++j) {
                std::cout << scores[i][j] << " ";
            }
            std::cout << std::endl;
        }

        // Debug: 打印前几帧的信息
        if (i < 5) {
            std::cout << "Frame " << i << ": decibel=" << cur_decibel
                      << ", snr=" << cur_snr << ", sil_prob=" << sil_prob
                      << ", speech_prob=" << speech_prob
                      << ", threshold=" << threshold << std::endl;
        }

        // 简化的判断逻辑：直接使用语音概率
        if (speech_prob > threshold) {
            if (cur_snr >= snr_thres && cur_decibel >= decibel_thres) {
                frame_states.push_back(SPEECH);
            } else {
                frame_states.push_back(SILENCE);
            }
        } else {
            frame_states.push_back(SILENCE);
            // 更新噪声平均分贝
            if (noise_average_decibel < -99.9f) {
                noise_average_decibel = cur_decibel;
            } else {
                noise_average_decibel = (cur_decibel + noise_average_decibel * 99.0f) / 100.0f;
            }
        }
    }

    // 基于状态序列提取语音段（简化版本）
    bool in_speech = false;
    int speech_start = 0;
    int min_speech_frames = 5;   // 最小语音帧数 (50ms)
    int max_silence_frames = 80; // 最大静音帧数 (800ms)
    int silence_count = 0;

    for (size_t i = 0; i < frame_states.size(); ++i) {
        if (frame_states[i] == SPEECH) {
            if (!in_speech) {
                // 开始语音段
                in_speech = true;
                speech_start = i;
                silence_count = 0;
            } else {
                // 继续语音段，重置静音计数
                silence_count = 0;
            }
        } else if (frame_states[i] == SILENCE && in_speech) {
            silence_count++;
            if (silence_count >= max_silence_frames) {
                // 结束语音段
                int speech_end = i - silence_count;
                if (speech_end - speech_start >= min_speech_frames) {
                    int start_ms = speech_start * 160 * 1000 / 16000;
                    int end_ms = speech_end * 160 * 1000 / 16000;
                    segments.push_back({start_ms, end_ms});
                    std::cout << "检测到语音段: " << start_ms << "ms - " << end_ms << "ms" << std::endl;
                }
                in_speech = false;
                silence_count = 0;
            }
        }
    }

    // 处理最后的语音段
    if (in_speech && is_final) {
        int speech_end = frame_states.size() - silence_count;
        if (speech_end - speech_start >= min_speech_frames) {
            int start_ms = speech_start * 160 * 1000 / 16000;
            int end_ms = speech_end * 160 * 1000 / 16000;
            segments.push_back({start_ms, end_ms});
            std::cout << "检测到最后语音段: " << start_ms << "ms - " << end_ms << "ms" << std::endl;
        }
    }

    return segments;
}

std::vector<std::vector<int>> E2EVadModel::MergeSegments(
    const std::vector<std::vector<int>>& segments,
    int max_length,
    int min_length) {
    
    if (segments.size() <= 1) return segments;
    
    std::vector<std::vector<int>> merged_segments;
    std::vector<int> time_points;
    
    // Collect all time points
    for (const auto& seg : segments) {
        time_points.push_back(seg[0]);
        time_points.push_back(seg[1]);
    }
    
    // Sort and remove duplicates
    std::sort(time_points.begin(), time_points.end());
    time_points.erase(std::unique(time_points.begin(), time_points.end()), time_points.end());
    
    if (time_points.empty()) return {};
    
    int bg = 0;
    for (size_t i = 0; i < time_points.size() - 1; ++i) {
        int time = time_points[i];
        if (time_points[i + 1] - bg < max_length) {
            continue;
        }
        if (time - bg > min_length) {
            merged_segments.push_back({bg, time});
        }
        bg = time;
    }
    merged_segments.push_back({bg, time_points.back()});
    
    return merged_segments;
}

// FSMNVADOpenVINO implementation
FSMNVADOpenVINO::FSMNVADOpenVINO(const std::string& model_dir, const std::string& device, int batch_size)
    : model_dir_(model_dir), device_(device), batch_size_(batch_size) {
    
    LoadModel();
    LoadConfig();
    InitializeCache();
    
    std::cout << "✓ FSMN-VAD OpenVINO模型加载成功" << std::endl;
    std::cout << "  模型目录: " << model_dir << std::endl;
    std::cout << "  推理设备: " << device << std::endl;
}

void FSMNVADOpenVINO::LoadModel() {
    std::string model_file = model_dir_ + "/model.xml";
    
    if (!std::filesystem::exists(model_file)) {
        throw std::runtime_error("OpenVINO模型不存在: " + model_file);
    }
    
    model_ = core_.read_model(model_file);
    compiled_model_ = core_.compile_model(model_, device_);
}

void FSMNVADOpenVINO::LoadConfig() {
    std::string config_file = model_dir_ + "/config.yaml";
    std::string cmvn_file = model_dir_ + "/am.mvn";
    
    if (!std::filesystem::exists(config_file)) {
        throw std::runtime_error("配置文件不存在: " + config_file);
    }
    
    config_ = ConfigUtils::LoadVADConfig(config_file);
    frontend_ = std::make_unique<WavFrontend>(cmvn_file, config_.lfr_m, config_.lfr_n);
}

void FSMNVADOpenVINO::InitializeCache() {
    // Initialize FSMN cache tensors
    for (int i = 0; i < config_.fsmn_layers; ++i) {
        ov::Shape cache_shape = {1, static_cast<size_t>(config_.proj_dim), 
                                static_cast<size_t>(config_.lorder - 1), 1};
        ov::Tensor cache_tensor(ov::element::f32, cache_shape);
        
        // Initialize with zeros
        float* cache_data = cache_tensor.data<float>();
        std::fill(cache_data, cache_data + cache_tensor.get_size(), 0.0f);
        
        cache_tensors_.push_back(cache_tensor);
    }
}

std::vector<VADResult> FSMNVADOpenVINO::Generate(const std::string& input_audio) {
    // Load audio
    auto waveform = AudioUtils::LoadAudio(input_audio, 16000);
    std::vector<std::vector<float>> waveform_list = {waveform};
    
    // Initialize segments
    std::vector<std::vector<std::vector<int>>> segments(1);
    
    // Extract features
    auto [feats, feats_len] = ExtractFeatures(waveform_list);
    
    // Create VAD scorer
    E2EVadModel vad_scorer(config_);
    
    // Process in chunks
    int t_offset = 0;
    int step = std::min(feats_len[0], 6000);
    
    while (t_offset < feats_len[0]) {
        int current_step = std::min(step, feats_len[0] - t_offset);
        bool is_final = (t_offset + current_step >= feats_len[0] - 1);
        
        // Prepare current chunk
        std::vector<std::vector<std::vector<float>>> feats_package(1);
        feats_package[0].assign(feats[0].begin() + t_offset, 
                               feats[0].begin() + t_offset + current_step);
        
        // Run inference
        auto scores = RunInference(feats_package, cache_tensors_);
        
        // Prepare waveform package
        int start_sample = t_offset * 160;
        int end_sample = std::min(static_cast<int>(waveform.size()), 
                                 (t_offset + current_step - 1) * 160 + 400);
        std::vector<std::vector<float>> waveform_package(1);
        waveform_package[0].assign(waveform.begin() + start_sample, 
                                  waveform.begin() + end_sample);
        
        // VAD post-processing
        auto segments_part = vad_scorer(scores, waveform_package, is_final, config_.max_end_silence_time, false);
        
        // Merge segments
        if (!segments_part.empty() && !segments_part[0].empty()) {
            segments[0].insert(segments[0].end(), 
                              segments_part[0].begin(), segments_part[0].end());
        }
        
        t_offset += current_step;
    }
    
    // Return FunASR format result
    std::string key = std::filesystem::path(input_audio).stem().string();
    return {{key, segments[0]}};
}

std::pair<std::vector<std::vector<std::vector<float>>>, std::vector<int>> FSMNVADOpenVINO::ExtractFeatures(
    const std::vector<std::vector<float>>& waveform_list) {

    std::vector<std::vector<std::vector<float>>> feats_list;
    std::vector<int> feats_len_list;

    for (const auto& waveform : waveform_list) {
        auto [speech, _] = frontend_->Fbank(waveform);
        auto [feat, feat_len] = frontend_->LfrCmvn(speech);
        feats_list.push_back(feat);
        feats_len_list.push_back(feat_len);
    }

    // Pad features to same length
    int max_feat_len = *std::max_element(feats_len_list.begin(), feats_len_list.end());
    auto padded_feats = PadFeatures(feats_list, max_feat_len);

    return {padded_feats, feats_len_list};
}

std::vector<std::vector<std::vector<float>>> FSMNVADOpenVINO::RunInference(
    const std::vector<std::vector<std::vector<float>>>& features,
    std::vector<ov::Tensor>& cache) {

    if (features.empty() || features[0].empty()) {
        return {};
    }

    try {
        // Create inference request
        auto infer_request = compiled_model_.create_infer_request();

        // Get input tensor info and create properly sized tensor
        auto input_port = compiled_model_.input(0);

        // Calculate actual shape based on data
        size_t batch_size = features.size();
        size_t seq_len = features[0].size();
        size_t feature_dim = features[0][0].size();

        ov::Shape actual_shape = {batch_size, seq_len, feature_dim};
        ov::Tensor input_tensor(input_port.get_element_type(), actual_shape);

        // Copy features to input tensor
        float* input_data = input_tensor.data<float>();
        size_t offset = 0;
        for (const auto& batch : features) {
            for (const auto& frame : batch) {
                for (float val : frame) {
                    input_data[offset++] = val;
                }
            }
        }

        // Set the input tensor
        infer_request.set_input_tensor(0, input_tensor);

        // Set cache inputs (if any)
        for (size_t i = 0; i < cache.size() && i + 1 < compiled_model_.inputs().size(); ++i) {
            infer_request.set_input_tensor(i + 1, cache[i]);
        }

        // Run inference
        infer_request.infer();

        // Get outputs
        auto output_tensor = infer_request.get_output_tensor(0);
        float* output_data = output_tensor.data<float>();
        auto output_shape = output_tensor.get_shape();

        // Update cache tensors (if any)
        for (size_t i = 0; i < cache.size() && i + 1 < compiled_model_.outputs().size(); ++i) {
            cache[i] = infer_request.get_output_tensor(i + 1);
        }

        // Convert output to scores format
        std::vector<std::vector<std::vector<float>>> scores(1);

        std::cout << "VAD推理输出形状: [";
        for (size_t i = 0; i < output_shape.size(); ++i) {
            std::cout << output_shape[i];
            if (i < output_shape.size() - 1) std::cout << ", ";
        }
        std::cout << "]" << std::endl;

        // 检查输出数据的前几个值
        std::cout << "VAD输出前10个值: ";
        for (size_t i = 0; i < std::min(output_tensor.get_size(), size_t(10)); ++i) {
            std::cout << output_data[i] << " ";
        }
        std::cout << std::endl;

        if (output_shape.size() >= 3) {
            size_t out_batch_size = output_shape[0];
            size_t out_seq_len = output_shape[1];
            size_t num_classes = output_shape[2];

            std::cout << "解析VAD输出: batch=" << out_batch_size
                      << ", seq_len=" << out_seq_len
                      << ", num_classes=" << num_classes << std::endl;

            scores[0].resize(out_seq_len);
            for (size_t t = 0; t < out_seq_len; ++t) {
                scores[0][t].resize(num_classes);
                for (size_t c = 0; c < num_classes; ++c) {
                    scores[0][t][c] = output_data[t * num_classes + c];
                }
            }
        } else if (output_shape.size() == 2) {
            // 可能是 [seq_len, num_classes] 格式
            size_t out_seq_len = output_shape[0];
            size_t num_classes = output_shape[1];

            std::cout << "解析VAD输出(2D): seq_len=" << out_seq_len
                      << ", num_classes=" << num_classes << std::endl;

            scores[0].resize(out_seq_len);
            for (size_t t = 0; t < out_seq_len; ++t) {
                scores[0][t].resize(num_classes);
                for (size_t c = 0; c < num_classes; ++c) {
                    scores[0][t][c] = output_data[t * num_classes + c];
                }
            }
        } else {
            // Fallback for unexpected output shape
            std::cerr << "Warning: Unexpected output shape" << std::endl;
        }

        return scores;

    } catch (const std::exception& e) {
        std::cerr << "Error in VAD inference: " << e.what() << std::endl;
        return {};
    }
}

std::vector<std::vector<std::vector<float>>> FSMNVADOpenVINO::PadFeatures(
    const std::vector<std::vector<std::vector<float>>>& features,
    int max_length) {

    std::vector<std::vector<std::vector<float>>> padded_features;

    for (const auto& feat : features) {
        std::vector<std::vector<float>> padded_feat = feat;

        // Pad with zeros if necessary
        while (static_cast<int>(padded_feat.size()) < max_length) {
            if (!feat.empty()) {
                std::vector<float> zero_frame(feat[0].size(), 0.0f);
                padded_feat.push_back(zero_frame);
            }
        }

        // Truncate if too long
        if (static_cast<int>(padded_feat.size()) > max_length) {
            padded_feat.resize(max_length);
        }

        padded_features.push_back(padded_feat);
    }

    return padded_features;
}

} // namespace funasr_openvino
