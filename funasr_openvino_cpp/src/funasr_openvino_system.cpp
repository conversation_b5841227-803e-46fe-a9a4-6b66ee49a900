#include "../include/funasr_openvino_system.h"
#include <filesystem>
#include <algorithm>

namespace funasr_openvino {

// FunASROpenVINOSystem implementation
FunASROpenVINOSystem::FunASROpenVINOSystem(
    const std::string& vad_model_dir,
    const std::string& asr_model_path,
    const std::string& device) {
    
    std::cout << "============================================================" << std::endl;
    std::cout << "初始化FunASR官方inference_with_vad + OpenVINO系统" << std::endl;
    std::cout << "============================================================" << std::endl;
    
    timer_.Start();
    
    // Initialize VAD model
    std::cout << "🔄 初始化VAD模型..." << std::endl;
    Timer vad_timer;
    vad_timer.Start();
    vad_model_ = std::make_unique<FSMNVADOpenVINO>(vad_model_dir, device);
    double vad_init_time = vad_timer.ElapsedSeconds();
    std::cout << "✅ VAD模型初始化完成，耗时: " << vad_init_time << "秒" << std::endl;
    
    // Initialize ASR model
    std::cout << "🔄 初始化ASR OpenVINO模型..." << std::endl;
    Timer asr_timer;
    asr_timer.Start();
    asr_model_ = std::make_unique<SenseVoiceOpenVINO>(asr_model_path, device);
    double asr_init_time = asr_timer.ElapsedSeconds();
    std::cout << "✅ ASR模型初始化完成，耗时: " << asr_init_time << "秒" << std::endl;
    
    double total_init_time = timer_.ElapsedSeconds();
    std::cout << "✅ FunASR官方inference_with_vad + OpenVINO系统初始化完成" << std::endl;
    std::cout << "📊 模型加载时间统计:" << std::endl;
    std::cout << "   VAD模型加载: " << vad_init_time << "秒" << std::endl;
    std::cout << "   ASR模型加载: " << asr_init_time << "秒" << std::endl;
    std::cout << "   总加载时间: " << total_init_time << "秒" << std::endl;
}

std::vector<ASRResult> FunASROpenVINOSystem::InferenceWithVAD(
    const std::string& input_audio,
    const InferenceConfig& config) {
    
    std::cout << "\n============================================================" << std::endl;
    std::cout << "开始FunASR官方inference_with_vad + OpenVINO推理" << std::endl;
    std::cout << "============================================================" << std::endl;
    std::cout << "音频文件: " << input_audio << std::endl;
    
    timer_.Start();
    
    // Step 1: VAD detection
    std::cout << "\n🔄 步骤1: VAD检测..." << std::endl;
    Timer vad_timer;
    vad_timer.Start();
    auto vad_res = vad_model_->Generate(input_audio);
    double vad_time = vad_timer.ElapsedSeconds();
    std::cout << "✓ VAD检测完成，耗时: " << vad_time << "秒" << std::endl;
    std::cout << "✓ 检测到 " << vad_res[0].segments.size() << " 个语音段" << std::endl;
    
    // Step 2: VAD merging (if enabled)
    if (config.merge_vad) {
        std::cout << "🔄 步骤2: VAD段合并..." << std::endl;
        for (auto& result : vad_res) {
            result.segments = MergeVAD(result.segments, config.merge_length_s * 1000, 0);
        }
        std::cout << "✓ 合并后剩余 " << vad_res[0].segments.size() << " 个语音段" << std::endl;
    }
    
    // Step 3: ASR inference
    std::cout << "🔄 步骤3: ASR推理..." << std::endl;
    Timer asr_timer;
    asr_timer.Start();
    
    // Prepare data
    auto [key_list, data_list] = PrepareDataIterator(input_audio);
    std::vector<ASRResult> results_ret_list;
    
    for (size_t i = 0; i < vad_res.size(); ++i) {
        const std::string& key = vad_res[i].key;
        const auto& vad_segments = vad_res[i].segments;
        const std::string& input_i = data_list[i];
        
        // Load audio
        auto speech = utils::LoadAudioTextImageVideo(input_i, 16000, 16000);
        int speech_length = speech.size();
        
        if (vad_segments.empty()) {
            results_ret_list.push_back({key, "", {}});
            continue;
        }
        
        std::vector<std::string> all_texts;
        
        // Choose processing strategy based on device
        if (config.device == "CPU") {
            all_texts = ProcessCPUMode(vad_segments, speech, speech_length, config);
        } else {
            all_texts = ProcessGPUMode(vad_segments, speech, speech_length, config);
        }
        
        // Merge texts
        std::string final_text = MergeTexts(all_texts);
        results_ret_list.push_back({key, final_text, {}});
    }
    
    double asr_time = asr_timer.ElapsedSeconds();
    double total_time = timer_.ElapsedSeconds();
    
    std::cout << "\n📊 详细时间统计:" << std::endl;
    std::cout << "   VAD检测时间: " << vad_time << "秒" << std::endl;
    std::cout << "   ASR推理时间: " << asr_time << "秒" << std::endl;
    std::cout << "   总推理时间: " << total_time << "秒" << std::endl;
    
    return results_ret_list;
}

std::pair<std::vector<std::string>, std::vector<std::string>> FunASROpenVINOSystem::PrepareDataIterator(
    const std::string& input_data) {
    
    std::string key = std::filesystem::path(input_data).stem().string();
    return {{key}, {input_data}};
}

std::vector<std::vector<int>> FunASROpenVINOSystem::MergeVAD(
    const std::vector<std::vector<int>>& vad_result,
    int max_length,
    int min_length) {
    
    if (vad_result.size() <= 1) return vad_result;
    
    std::vector<std::vector<int>> new_result;
    std::vector<int> time_step;
    
    // Collect all time points
    for (const auto& segment : vad_result) {
        time_step.push_back(segment[0]);
        time_step.push_back(segment[1]);
    }
    
    // Sort and remove duplicates
    std::sort(time_step.begin(), time_step.end());
    time_step.erase(std::unique(time_step.begin(), time_step.end()), time_step.end());
    
    if (time_step.empty()) return {};
    
    int bg = 0;
    for (size_t i = 0; i < time_step.size() - 1; ++i) {
        int time = time_step[i];
        if (time_step[i + 1] - bg < max_length) {
            continue;
        }
        if (time - bg > min_length) {
            new_result.push_back({bg, time});
        }
        bg = time;
    }
    new_result.push_back({bg, time_step.back()});
    
    return new_result;
}

std::pair<std::vector<std::vector<float>>, std::vector<int>> FunASROpenVINOSystem::SlicePaddingAudioSamples(
    const std::vector<float>& speech,
    int speech_length,
    const std::vector<std::vector<int>>& vad_segments) {
    
    std::vector<std::vector<float>> speech_list;
    std::vector<int> speech_lengths_list;
    
    for (const auto& segment : vad_segments) {
        int bed_idx = segment[0] * 16;  // Convert ms to samples
        int end_idx = std::min(segment[1] * 16, speech_length);
        
        std::vector<float> speech_i(speech.begin() + bed_idx, speech.begin() + end_idx);
        int speech_length_i = end_idx - bed_idx;
        
        speech_list.push_back(speech_i);
        speech_lengths_list.push_back(speech_length_i);
    }
    
    return {speech_list, speech_lengths_list};
}

std::vector<std::string> FunASROpenVINOSystem::ProcessCPUMode(
    const std::vector<std::vector<int>>& vad_segments,
    const std::vector<float>& speech,
    int speech_length,
    const InferenceConfig& config) {

    std::cout << "🔥 使用CPU优化模式：逐个处理音频段" << std::endl;

    // Extract audio segments
    auto audio_segments = ExtractAudioSegments(speech, vad_segments);

    std::cout << "✓ 提取了 " << audio_segments.size() << " 个音频段" << std::endl;

    // Use fallback mode (single file processing) for now to avoid batch issues
    std::vector<std::string> results;
    for (size_t i = 0; i < audio_segments.size(); ++i) {
        try {
            std::cout << "处理音频段 " << (i+1) << "/" << audio_segments.size() << std::endl;

            // Save to temporary file and process
            std::string temp_file = "/tmp/temp_audio_" + std::to_string(i) + ".wav";
            AudioUtils::SaveAudio(temp_file, audio_segments[i], 16000);

            std::string result = asr_model_->InferenceAudioFile(temp_file, config.language);
            if (!result.empty()) {
                results.push_back(result);
            }

            // Clean up
            std::filesystem::remove(temp_file);

        } catch (const std::exception& e) {
            std::cerr << "⚠️ 音频段 " << (i+1) << " 推理失败: " << e.what() << std::endl;
            results.push_back("");
        }
    }

    std::cout << "✓ 完成 " << results.size() << " 个音频段的处理" << std::endl;
    return results;
}

std::vector<ASRResult> FunASROpenVINOSystem::DirectASRInference(
    const std::string& input_audio,
    const InferenceConfig& config) {

    std::vector<ASRResult> results;

    std::cout << "\n============================================================" << std::endl;
    std::cout << "开始直接ASR推理（跳过VAD）" << std::endl;
    std::cout << "============================================================" << std::endl;
    std::cout << "音频文件: " << input_audio << std::endl;

    try {
        // 加载音频
        std::cout << "\n🔄 加载音频..." << std::endl;
        Timer audio_timer;
        audio_timer.Start();

        std::vector<float> audio_data = AudioUtils::LoadAudio(input_audio, 16000);
        if (audio_data.empty()) {
            std::cerr << "Error: Failed to load audio file: " << input_audio << std::endl;
            return results;
        }

        double audio_time = audio_timer.ElapsedSeconds();
        std::cout << "✓ 音频加载完成，耗时: " << audio_time << "秒" << std::endl;
        std::cout << "✓ 音频长度: " << audio_data.size() << " 采样点, "
                  << (float)audio_data.size() / 16000.0f << " 秒" << std::endl;

        // 直接进行ASR推理
        std::cout << "\n🔄 ASR推理..." << std::endl;
        Timer asr_timer;
        asr_timer.Start();

        // 提取文件名作为key
        std::string key = input_audio;
        size_t last_slash = key.find_last_of("/\\");
        if (last_slash != std::string::npos) {
            key = key.substr(last_slash + 1);
        }
        size_t last_dot = key.find_last_of(".");
        if (last_dot != std::string::npos) {
            key = key.substr(0, last_dot);
        }

        // 进行ASR推理
        std::string text = asr_model_->InferenceAudioFile(input_audio, config.language);

        double asr_time = asr_timer.ElapsedSeconds();
        std::cout << "✓ ASR推理完成，耗时: " << asr_time << "秒" << std::endl;

        // 创建结果
        ASRResult result;
        result.key = key;
        result.text = text;
        results.push_back(result);

        // 输出时间统计
        std::cout << "\n📊 详细时间统计:" << std::endl;
        std::cout << "   音频加载时间: " << audio_time << "秒" << std::endl;
        std::cout << "   ASR推理时间: " << asr_time << "秒" << std::endl;
        std::cout << "   总推理时间: " << (audio_time + asr_time) << "秒" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "Error during direct ASR inference: " << e.what() << std::endl;
    }

    return results;
}

std::vector<std::string> FunASROpenVINOSystem::ProcessGPUMode(
    const std::vector<std::vector<int>>& vad_segments,
    const std::vector<float>& speech,
    int speech_length,
    const InferenceConfig& config) {
    
    std::cout << "🔥 使用GPU批处理模式：智能分组处理" << std::endl;
    
    // Extract audio segments
    auto audio_segments = ExtractAudioSegments(speech, vad_segments);
    
    // Use batch inference
    return asr_model_->InferenceBatchDirect(audio_segments, config.language);
}

std::string FunASROpenVINOSystem::MergeTexts(const std::vector<std::string>& texts) {
    if (texts.empty()) return "";
    
    std::string final_text;
    for (size_t i = 0; i < texts.size(); ++i) {
        if (i == 0) {
            final_text = texts[i];
        } else {
            // Smart text merging
            if (!final_text.empty() && final_text.back() != ' ' && 
                texts[i].find_first_of("。！？，、；：") != 0) {
                final_text += " ";
            }
            final_text += texts[i];
        }
    }
    
    return final_text;
}

std::vector<std::vector<float>> FunASROpenVINOSystem::ExtractAudioSegments(
    const std::vector<float>& speech,
    const std::vector<std::vector<int>>& vad_segments,
    int sample_rate) {
    
    std::vector<std::vector<float>> audio_segments;
    
    for (const auto& segment : vad_segments) {
        int start_sample = segment[0] * sample_rate / 1000;  // Convert ms to samples
        int end_sample = segment[1] * sample_rate / 1000;
        end_sample = std::min(end_sample, static_cast<int>(speech.size()));
        
        if (end_sample > start_sample) {
            std::vector<float> audio_segment(speech.begin() + start_sample, 
                                           speech.begin() + end_sample);
            audio_segments.push_back(audio_segment);
        }
    }
    
    return audio_segments;
}

// Utility functions
namespace utils {

std::vector<float> LoadAudioTextImageVideo(
    const std::string& input_path,
    int fs,
    int audio_fs) {
    
    return AudioUtils::LoadAudio(input_path, fs);
}

std::string RichTranscriptionPostprocess(const std::string& text) {
    // Simplified post-processing
    // In real implementation, this would handle rich transcription markers
    return text;
}

} // namespace utils

} // namespace funasr_openvino
