# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /media/DataWork/code/sensevoice0825/funasr_openvino_cpp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Install the project..."
	/usr/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing only the local directory..."
	/usr/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Installing the project stripped..."
	/usr/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build/CMakeFiles /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named funasr_openvino_lib

# Build rule for target.
funasr_openvino_lib: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 funasr_openvino_lib
.PHONY : funasr_openvino_lib

# fast build rule for target.
funasr_openvino_lib/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/funasr_openvino_lib.dir/build.make CMakeFiles/funasr_openvino_lib.dir/build
.PHONY : funasr_openvino_lib/fast

#=============================================================================
# Target rules for targets named funasr_openvino_main

# Build rule for target.
funasr_openvino_main: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 funasr_openvino_main
.PHONY : funasr_openvino_main

# fast build rule for target.
funasr_openvino_main/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/funasr_openvino_main.dir/build.make CMakeFiles/funasr_openvino_main.dir/build
.PHONY : funasr_openvino_main/fast

bin/funasr_openvino_main.o: bin/funasr_openvino_main.cpp.o
.PHONY : bin/funasr_openvino_main.o

# target to build an object file
bin/funasr_openvino_main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/funasr_openvino_main.dir/build.make CMakeFiles/funasr_openvino_main.dir/bin/funasr_openvino_main.cpp.o
.PHONY : bin/funasr_openvino_main.cpp.o

bin/funasr_openvino_main.i: bin/funasr_openvino_main.cpp.i
.PHONY : bin/funasr_openvino_main.i

# target to preprocess a source file
bin/funasr_openvino_main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/funasr_openvino_main.dir/build.make CMakeFiles/funasr_openvino_main.dir/bin/funasr_openvino_main.cpp.i
.PHONY : bin/funasr_openvino_main.cpp.i

bin/funasr_openvino_main.s: bin/funasr_openvino_main.cpp.s
.PHONY : bin/funasr_openvino_main.s

# target to generate assembly for a file
bin/funasr_openvino_main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/funasr_openvino_main.dir/build.make CMakeFiles/funasr_openvino_main.dir/bin/funasr_openvino_main.cpp.s
.PHONY : bin/funasr_openvino_main.cpp.s

src/common.o: src/common.cpp.o
.PHONY : src/common.o

# target to build an object file
src/common.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/funasr_openvino_lib.dir/build.make CMakeFiles/funasr_openvino_lib.dir/src/common.cpp.o
.PHONY : src/common.cpp.o

src/common.i: src/common.cpp.i
.PHONY : src/common.i

# target to preprocess a source file
src/common.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/funasr_openvino_lib.dir/build.make CMakeFiles/funasr_openvino_lib.dir/src/common.cpp.i
.PHONY : src/common.cpp.i

src/common.s: src/common.cpp.s
.PHONY : src/common.s

# target to generate assembly for a file
src/common.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/funasr_openvino_lib.dir/build.make CMakeFiles/funasr_openvino_lib.dir/src/common.cpp.s
.PHONY : src/common.cpp.s

src/fsmn_vad_openvino.o: src/fsmn_vad_openvino.cpp.o
.PHONY : src/fsmn_vad_openvino.o

# target to build an object file
src/fsmn_vad_openvino.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/funasr_openvino_lib.dir/build.make CMakeFiles/funasr_openvino_lib.dir/src/fsmn_vad_openvino.cpp.o
.PHONY : src/fsmn_vad_openvino.cpp.o

src/fsmn_vad_openvino.i: src/fsmn_vad_openvino.cpp.i
.PHONY : src/fsmn_vad_openvino.i

# target to preprocess a source file
src/fsmn_vad_openvino.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/funasr_openvino_lib.dir/build.make CMakeFiles/funasr_openvino_lib.dir/src/fsmn_vad_openvino.cpp.i
.PHONY : src/fsmn_vad_openvino.cpp.i

src/fsmn_vad_openvino.s: src/fsmn_vad_openvino.cpp.s
.PHONY : src/fsmn_vad_openvino.s

# target to generate assembly for a file
src/fsmn_vad_openvino.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/funasr_openvino_lib.dir/build.make CMakeFiles/funasr_openvino_lib.dir/src/fsmn_vad_openvino.cpp.s
.PHONY : src/fsmn_vad_openvino.cpp.s

src/funasr_openvino_system.o: src/funasr_openvino_system.cpp.o
.PHONY : src/funasr_openvino_system.o

# target to build an object file
src/funasr_openvino_system.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/funasr_openvino_lib.dir/build.make CMakeFiles/funasr_openvino_lib.dir/src/funasr_openvino_system.cpp.o
.PHONY : src/funasr_openvino_system.cpp.o

src/funasr_openvino_system.i: src/funasr_openvino_system.cpp.i
.PHONY : src/funasr_openvino_system.i

# target to preprocess a source file
src/funasr_openvino_system.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/funasr_openvino_lib.dir/build.make CMakeFiles/funasr_openvino_lib.dir/src/funasr_openvino_system.cpp.i
.PHONY : src/funasr_openvino_system.cpp.i

src/funasr_openvino_system.s: src/funasr_openvino_system.cpp.s
.PHONY : src/funasr_openvino_system.s

# target to generate assembly for a file
src/funasr_openvino_system.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/funasr_openvino_lib.dir/build.make CMakeFiles/funasr_openvino_lib.dir/src/funasr_openvino_system.cpp.s
.PHONY : src/funasr_openvino_system.cpp.s

src/sensevoice_openvino.o: src/sensevoice_openvino.cpp.o
.PHONY : src/sensevoice_openvino.o

# target to build an object file
src/sensevoice_openvino.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/funasr_openvino_lib.dir/build.make CMakeFiles/funasr_openvino_lib.dir/src/sensevoice_openvino.cpp.o
.PHONY : src/sensevoice_openvino.cpp.o

src/sensevoice_openvino.i: src/sensevoice_openvino.cpp.i
.PHONY : src/sensevoice_openvino.i

# target to preprocess a source file
src/sensevoice_openvino.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/funasr_openvino_lib.dir/build.make CMakeFiles/funasr_openvino_lib.dir/src/sensevoice_openvino.cpp.i
.PHONY : src/sensevoice_openvino.cpp.i

src/sensevoice_openvino.s: src/sensevoice_openvino.cpp.s
.PHONY : src/sensevoice_openvino.s

# target to generate assembly for a file
src/sensevoice_openvino.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/funasr_openvino_lib.dir/build.make CMakeFiles/funasr_openvino_lib.dir/src/sensevoice_openvino.cpp.s
.PHONY : src/sensevoice_openvino.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... rebuild_cache"
	@echo "... funasr_openvino_lib"
	@echo "... funasr_openvino_main"
	@echo "... bin/funasr_openvino_main.o"
	@echo "... bin/funasr_openvino_main.i"
	@echo "... bin/funasr_openvino_main.s"
	@echo "... src/common.o"
	@echo "... src/common.i"
	@echo "... src/common.s"
	@echo "... src/fsmn_vad_openvino.o"
	@echo "... src/fsmn_vad_openvino.i"
	@echo "... src/fsmn_vad_openvino.s"
	@echo "... src/funasr_openvino_system.o"
	@echo "... src/funasr_openvino_system.i"
	@echo "... src/funasr_openvino_system.s"
	@echo "... src/sensevoice_openvino.o"
	@echo "... src/sensevoice_openvino.i"
	@echo "... src/sensevoice_openvino.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

