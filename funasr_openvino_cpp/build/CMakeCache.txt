# This is the CMakeCache file.
# For build in directory: /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build
# It was generated by CMake: /usr/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Build tests
BUILD_TESTS:BOOL=OFF

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=/usr/bin/addr2line

//Path to a program.
CMAKE_AR:FILEPATH=/usr/bin/ar

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//CXX compiler
CMAKE_CXX_COMPILER:FILEPATH=/usr/bin/c++

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-11

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-11

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//C compiler
CMAKE_C_COMPILER:FILEPATH=/usr/bin/cc

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_AR:FILEPATH=/usr/bin/gcc-ar-12

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_RANLIB:FILEPATH=/usr/bin/gcc-ranlib-12

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/usr/local

//Path to a program.
CMAKE_LINKER:FILEPATH=/usr/bin/ld

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/gmake

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=/usr/bin/objcopy

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/usr/bin/objdump

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=FunASROpenVINO

//Path to a program.
CMAKE_RANLIB:FILEPATH=/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=/usr/bin/readelf

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/usr/bin/strip

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Value Computed by CMake
FunASROpenVINO_BINARY_DIR:STATIC=/media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build

//Value Computed by CMake
FunASROpenVINO_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
FunASROpenVINO_SOURCE_DIR:STATIC=/media/DataWork/code/sensevoice0825/funasr_openvino_cpp

//The directory containing a CMake configuration file for OpenVINO.
OpenVINO_DIR:PATH=/opt/intel/openvino_genai_2025.2.0/runtime/cmake

//Arguments to supply to pkg-config
PKG_CONFIG_ARGN:STRING=

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=/usr/bin/pkg-config

//Path to a library.
pkgcfg_lib_FFMPEG_avcodec:FILEPATH=/usr/lib/x86_64-linux-gnu/libavcodec.so

//Path to a library.
pkgcfg_lib_FFMPEG_avformat:FILEPATH=/usr/lib/x86_64-linux-gnu/libavformat.so

//Path to a library.
pkgcfg_lib_FFMPEG_avutil:FILEPATH=/usr/lib/x86_64-linux-gnu/libavutil.so

//Path to a library.
pkgcfg_lib_FFMPEG_swresample:FILEPATH=/usr/lib/x86_64-linux-gnu/libswresample.so

//Path to a library.
pkgcfg_lib_SENTENCEPIECE_sentencepiece:FILEPATH=/usr/lib/x86_64-linux-gnu/libsentencepiece.so

//Path to a library.
pkgcfg_lib_SENTENCEPIECE_sentencepiece_train:FILEPATH=/usr/lib/x86_64-linux-gnu/libsentencepiece_train.so

//Path to a library.
pkgcfg_lib_SNDFILE_sndfile:FILEPATH=/usr/lib/x86_64-linux-gnu/libsndfile.so

//The directory containing a CMake configuration file for yaml-cpp.
yaml-cpp_DIR:PATH=/home/<USER>/miniconda3/lib/cmake/yaml-cpp


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=22
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=1
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/usr/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/usr/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/usr/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_AR
CMAKE_C_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_RANLIB
CMAKE_C_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Test CMAKE_HAVE_LIBC_PTHREAD
CMAKE_HAVE_LIBC_PTHREAD:INTERNAL=1
//Have include pthread.h
CMAKE_HAVE_PTHREAD_H:INTERNAL=1
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/media/DataWork/code/sensevoice0825/funasr_openvino_cpp
//Install .so files without execute permission.
CMAKE_INSTALL_SO_NO_EXE:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/usr/share/cmake-3.22
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
FFMPEG_CFLAGS:INTERNAL=-I/usr/include/x86_64-linux-gnu
FFMPEG_CFLAGS_I:INTERNAL=
FFMPEG_CFLAGS_OTHER:INTERNAL=
FFMPEG_FOUND:INTERNAL=1
FFMPEG_INCLUDEDIR:INTERNAL=
FFMPEG_INCLUDE_DIRS:INTERNAL=/usr/include/x86_64-linux-gnu
FFMPEG_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lavformat;-lavcodec;-lavutil;-lswresample
FFMPEG_LDFLAGS_OTHER:INTERNAL=
FFMPEG_LIBDIR:INTERNAL=
FFMPEG_LIBRARIES:INTERNAL=avformat;avcodec;avutil;swresample
FFMPEG_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
FFMPEG_LIBS:INTERNAL=
FFMPEG_LIBS_L:INTERNAL=
FFMPEG_LIBS_OTHER:INTERNAL=
FFMPEG_LIBS_PATHS:INTERNAL=
FFMPEG_MODULE_NAME:INTERNAL=
FFMPEG_PREFIX:INTERNAL=
FFMPEG_STATIC_CFLAGS:INTERNAL=-I/usr/include/x86_64-linux-gnu
FFMPEG_STATIC_CFLAGS_I:INTERNAL=
FFMPEG_STATIC_CFLAGS_OTHER:INTERNAL=
FFMPEG_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/x86_64-linux-gnu
FFMPEG_STATIC_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lavformat;-lm;-lxml2;-lbz2;-lgme;-lopenmpt;-lstdc++;-lchromaprint;-lbluray;-lz;-lgnutls;-lrabbitmq;-lsrt-gnutls;-lssh;-lzmq;-lavcodec;-lvpx;-lm;-lvpx;-lm;-lvpx;-lm;-lvpx;-lm;-lwebpmux;-lwebp;-lm;-llzma;-ldav1d;-lrsvg-2;-lm;-lgio-2.0;-lgdk_pixbuf-2.0;-lgobject-2.0;-lglib-2.0;-lcairo;-lzvbi;-lm;-lpthread;-lm;-lpng;-lz;-lsnappy;-lstdc++;-lz;-laom;-lcodec2;-lgsm;-lmp3lame;-lm;-lopenjp2;-lopus;-lm;-lshine;-lspeex;-ltheoraenc;-ltheoradec;-logg;-ltwolame;-lvorbis;-lvorbisenc;-lwebp;-lx264;-lx265;-lxvidcore;-pthread;-lva;-lmfx;-lstdc++;-ldl;-lswresample;-lm;-lsoxr;-lavutil;-pthread;-lva-drm;-lva;-lva-x11;-lva;-lvdpau;-lX11;-lm;-ldrm;-lmfx;-lstdc++;-ldl;-lOpenCL;-lva;-lXv;-lX11;-lXext
FFMPEG_STATIC_LDFLAGS_OTHER:INTERNAL=-pthread
FFMPEG_STATIC_LIBDIR:INTERNAL=
FFMPEG_STATIC_LIBRARIES:INTERNAL=avformat;m;xml2;bz2;gme;openmpt;stdc++;chromaprint;bluray;z;gnutls;rabbitmq;srt-gnutls;ssh;zmq;avcodec;vpx;m;vpx;m;vpx;m;vpx;m;webpmux;webp;m;lzma;dav1d;rsvg-2;m;gio-2.0;gdk_pixbuf-2.0;gobject-2.0;glib-2.0;cairo;zvbi;m;pthread;m;png;z;snappy;stdc++;z;aom;codec2;gsm;mp3lame;m;openjp2;opus;m;shine;speex;theoraenc;theoradec;ogg;twolame;vorbis;vorbisenc;webp;x264;x265;xvidcore;va;mfx;stdc++;dl;swresample;m;soxr;avutil;va-drm;va;va-x11;va;vdpau;X11;m;drm;mfx;stdc++;dl;OpenCL;va;Xv;X11;Xext
FFMPEG_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
FFMPEG_STATIC_LIBS:INTERNAL=
FFMPEG_STATIC_LIBS_L:INTERNAL=
FFMPEG_STATIC_LIBS_OTHER:INTERNAL=
FFMPEG_STATIC_LIBS_PATHS:INTERNAL=
FFMPEG_VERSION:INTERNAL=
FFMPEG_libavcodec_INCLUDEDIR:INTERNAL=/usr/include/x86_64-linux-gnu
FFMPEG_libavcodec_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
FFMPEG_libavcodec_MODULE_NAME:INTERNAL=libavcodec
FFMPEG_libavcodec_PREFIX:INTERNAL=/usr
FFMPEG_libavcodec_VERSION:INTERNAL=58.134.100
FFMPEG_libavformat_INCLUDEDIR:INTERNAL=/usr/include/x86_64-linux-gnu
FFMPEG_libavformat_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
FFMPEG_libavformat_MODULE_NAME:INTERNAL=libavformat
FFMPEG_libavformat_PREFIX:INTERNAL=/usr
FFMPEG_libavformat_VERSION:INTERNAL=58.76.100
FFMPEG_libavutil_INCLUDEDIR:INTERNAL=/usr/include/x86_64-linux-gnu
FFMPEG_libavutil_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
FFMPEG_libavutil_MODULE_NAME:INTERNAL=libavutil
FFMPEG_libavutil_PREFIX:INTERNAL=/usr
FFMPEG_libavutil_VERSION:INTERNAL=56.70.100
FFMPEG_libswresample_INCLUDEDIR:INTERNAL=/usr/include/x86_64-linux-gnu
FFMPEG_libswresample_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
FFMPEG_libswresample_MODULE_NAME:INTERNAL=libswresample
FFMPEG_libswresample_PREFIX:INTERNAL=/usr
FFMPEG_libswresample_VERSION:INTERNAL=3.9.100
//Details about finding PkgConfig
FIND_PACKAGE_MESSAGE_DETAILS_PkgConfig:INTERNAL=[/usr/bin/pkg-config][v0.29.2()]
//Details about finding Threads
FIND_PACKAGE_MESSAGE_DETAILS_Threads:INTERNAL=[TRUE][v()]
//ADVANCED property for variable: PKG_CONFIG_ARGN
PKG_CONFIG_ARGN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
SENTENCEPIECE_CFLAGS:INTERNAL=
SENTENCEPIECE_CFLAGS_I:INTERNAL=
SENTENCEPIECE_CFLAGS_OTHER:INTERNAL=
SENTENCEPIECE_FOUND:INTERNAL=1
SENTENCEPIECE_INCLUDEDIR:INTERNAL=/usr/include
SENTENCEPIECE_INCLUDE_DIRS:INTERNAL=
SENTENCEPIECE_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lsentencepiece;-lsentencepiece_train
SENTENCEPIECE_LDFLAGS_OTHER:INTERNAL=
SENTENCEPIECE_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
SENTENCEPIECE_LIBRARIES:INTERNAL=sentencepiece;sentencepiece_train
SENTENCEPIECE_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
SENTENCEPIECE_LIBS:INTERNAL=
SENTENCEPIECE_LIBS_L:INTERNAL=
SENTENCEPIECE_LIBS_OTHER:INTERNAL=
SENTENCEPIECE_LIBS_PATHS:INTERNAL=
SENTENCEPIECE_MODULE_NAME:INTERNAL=sentencepiece
SENTENCEPIECE_PREFIX:INTERNAL=/usr
SENTENCEPIECE_STATIC_CFLAGS:INTERNAL=
SENTENCEPIECE_STATIC_CFLAGS_I:INTERNAL=
SENTENCEPIECE_STATIC_CFLAGS_OTHER:INTERNAL=
SENTENCEPIECE_STATIC_INCLUDE_DIRS:INTERNAL=
SENTENCEPIECE_STATIC_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lsentencepiece;-lsentencepiece_train
SENTENCEPIECE_STATIC_LDFLAGS_OTHER:INTERNAL=
SENTENCEPIECE_STATIC_LIBDIR:INTERNAL=
SENTENCEPIECE_STATIC_LIBRARIES:INTERNAL=sentencepiece;sentencepiece_train
SENTENCEPIECE_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
SENTENCEPIECE_STATIC_LIBS:INTERNAL=
SENTENCEPIECE_STATIC_LIBS_L:INTERNAL=
SENTENCEPIECE_STATIC_LIBS_OTHER:INTERNAL=
SENTENCEPIECE_STATIC_LIBS_PATHS:INTERNAL=
SENTENCEPIECE_VERSION:INTERNAL=0.1.96
SENTENCEPIECE_sentencepiece_INCLUDEDIR:INTERNAL=
SENTENCEPIECE_sentencepiece_LIBDIR:INTERNAL=
SENTENCEPIECE_sentencepiece_PREFIX:INTERNAL=
SENTENCEPIECE_sentencepiece_VERSION:INTERNAL=
SNDFILE_CFLAGS:INTERNAL=-I/usr/include/opus
SNDFILE_CFLAGS_I:INTERNAL=
SNDFILE_CFLAGS_OTHER:INTERNAL=
SNDFILE_FOUND:INTERNAL=1
SNDFILE_INCLUDEDIR:INTERNAL=/usr/include
SNDFILE_INCLUDE_DIRS:INTERNAL=/usr/include/opus
SNDFILE_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lsndfile
SNDFILE_LDFLAGS_OTHER:INTERNAL=
SNDFILE_LIBDIR:INTERNAL=/usr/lib/x86_64-linux-gnu
SNDFILE_LIBRARIES:INTERNAL=sndfile
SNDFILE_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
SNDFILE_LIBS:INTERNAL=
SNDFILE_LIBS_L:INTERNAL=
SNDFILE_LIBS_OTHER:INTERNAL=
SNDFILE_LIBS_PATHS:INTERNAL=
SNDFILE_MODULE_NAME:INTERNAL=sndfile
SNDFILE_PREFIX:INTERNAL=/usr
SNDFILE_STATIC_CFLAGS:INTERNAL=-I/usr/include/opus
SNDFILE_STATIC_CFLAGS_I:INTERNAL=
SNDFILE_STATIC_CFLAGS_OTHER:INTERNAL=
SNDFILE_STATIC_INCLUDE_DIRS:INTERNAL=/usr/include/opus
SNDFILE_STATIC_LDFLAGS:INTERNAL=-L/usr/lib/x86_64-linux-gnu;-lsndfile;-lFLAC;-lm;-lvorbisenc;-lvorbis;-lm;-logg;-lopus;-lm
SNDFILE_STATIC_LDFLAGS_OTHER:INTERNAL=
SNDFILE_STATIC_LIBDIR:INTERNAL=
SNDFILE_STATIC_LIBRARIES:INTERNAL=sndfile;FLAC;m;vorbisenc;vorbis;m;ogg;opus;m
SNDFILE_STATIC_LIBRARY_DIRS:INTERNAL=/usr/lib/x86_64-linux-gnu
SNDFILE_STATIC_LIBS:INTERNAL=
SNDFILE_STATIC_LIBS_L:INTERNAL=
SNDFILE_STATIC_LIBS_OTHER:INTERNAL=
SNDFILE_STATIC_LIBS_PATHS:INTERNAL=
SNDFILE_VERSION:INTERNAL=1.0.31
SNDFILE_sndfile_INCLUDEDIR:INTERNAL=
SNDFILE_sndfile_LIBDIR:INTERNAL=
SNDFILE_sndfile_PREFIX:INTERNAL=
SNDFILE_sndfile_VERSION:INTERNAL=
__pkg_config_arguments_FFMPEG:INTERNAL=REQUIRED;libavformat;libavcodec;libavutil;libswresample
__pkg_config_arguments_SENTENCEPIECE:INTERNAL=REQUIRED;sentencepiece
__pkg_config_arguments_SNDFILE:INTERNAL=REQUIRED;sndfile
__pkg_config_checked_FFMPEG:INTERNAL=1
__pkg_config_checked_SENTENCEPIECE:INTERNAL=1
__pkg_config_checked_SNDFILE:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_FFMPEG_avcodec
pkgcfg_lib_FFMPEG_avcodec-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_FFMPEG_avformat
pkgcfg_lib_FFMPEG_avformat-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_FFMPEG_avutil
pkgcfg_lib_FFMPEG_avutil-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_FFMPEG_swresample
pkgcfg_lib_FFMPEG_swresample-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_SENTENCEPIECE_sentencepiece
pkgcfg_lib_SENTENCEPIECE_sentencepiece-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_SENTENCEPIECE_sentencepiece_train
pkgcfg_lib_SENTENCEPIECE_sentencepiece_train-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_SNDFILE_sndfile
pkgcfg_lib_SNDFILE_sndfile-ADVANCED:INTERNAL=1
prefix_result:INTERNAL=/usr/lib/x86_64-linux-gnu

