# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /media/DataWork/code/sensevoice0825/funasr_openvino_cpp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/funasr_openvino_lib.dir/all
all: CMakeFiles/funasr_openvino_main.dir/all
.PHONY : all

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/funasr_openvino_lib.dir/clean
clean: CMakeFiles/funasr_openvino_main.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/funasr_openvino_lib.dir

# All Build rule for target.
CMakeFiles/funasr_openvino_lib.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/funasr_openvino_lib.dir/build.make CMakeFiles/funasr_openvino_lib.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/funasr_openvino_lib.dir/build.make CMakeFiles/funasr_openvino_lib.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build/CMakeFiles --progress-num=1,2,3,4,5 "Built target funasr_openvino_lib"
.PHONY : CMakeFiles/funasr_openvino_lib.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/funasr_openvino_lib.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build/CMakeFiles 5
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/funasr_openvino_lib.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build/CMakeFiles 0
.PHONY : CMakeFiles/funasr_openvino_lib.dir/rule

# Convenience name for target.
funasr_openvino_lib: CMakeFiles/funasr_openvino_lib.dir/rule
.PHONY : funasr_openvino_lib

# clean rule for target.
CMakeFiles/funasr_openvino_lib.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/funasr_openvino_lib.dir/build.make CMakeFiles/funasr_openvino_lib.dir/clean
.PHONY : CMakeFiles/funasr_openvino_lib.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/funasr_openvino_main.dir

# All Build rule for target.
CMakeFiles/funasr_openvino_main.dir/all: CMakeFiles/funasr_openvino_lib.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/funasr_openvino_main.dir/build.make CMakeFiles/funasr_openvino_main.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/funasr_openvino_main.dir/build.make CMakeFiles/funasr_openvino_main.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --progress-dir=/media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build/CMakeFiles --progress-num=6,7 "Built target funasr_openvino_main"
.PHONY : CMakeFiles/funasr_openvino_main.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/funasr_openvino_main.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build/CMakeFiles 7
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/funasr_openvino_main.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build/CMakeFiles 0
.PHONY : CMakeFiles/funasr_openvino_main.dir/rule

# Convenience name for target.
funasr_openvino_main: CMakeFiles/funasr_openvino_main.dir/rule
.PHONY : funasr_openvino_main

# clean rule for target.
CMakeFiles/funasr_openvino_main.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/funasr_openvino_main.dir/build.make CMakeFiles/funasr_openvino_main.dir/clean
.PHONY : CMakeFiles/funasr_openvino_main.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

