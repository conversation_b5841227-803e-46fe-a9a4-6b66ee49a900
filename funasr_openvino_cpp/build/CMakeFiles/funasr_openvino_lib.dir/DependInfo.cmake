
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "/media/DataWork/code/sensevoice0825/funasr_openvino_cpp/src/common.cpp" "CMakeFiles/funasr_openvino_lib.dir/src/common.cpp.o" "gcc" "CMakeFiles/funasr_openvino_lib.dir/src/common.cpp.o.d"
  "/media/DataWork/code/sensevoice0825/funasr_openvino_cpp/src/fsmn_vad_openvino.cpp" "CMakeFiles/funasr_openvino_lib.dir/src/fsmn_vad_openvino.cpp.o" "gcc" "CMakeFiles/funasr_openvino_lib.dir/src/fsmn_vad_openvino.cpp.o.d"
  "/media/DataWork/code/sensevoice0825/funasr_openvino_cpp/src/funasr_openvino_system.cpp" "CMakeFiles/funasr_openvino_lib.dir/src/funasr_openvino_system.cpp.o" "gcc" "CMakeFiles/funasr_openvino_lib.dir/src/funasr_openvino_system.cpp.o.d"
  "/media/DataWork/code/sensevoice0825/funasr_openvino_cpp/src/sensevoice_openvino.cpp" "CMakeFiles/funasr_openvino_lib.dir/src/sensevoice_openvino.cpp.o" "gcc" "CMakeFiles/funasr_openvino_lib.dir/src/sensevoice_openvino.cpp.o.d"
  )

# Targets to which this target links.
set(CMAKE_TARGET_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
