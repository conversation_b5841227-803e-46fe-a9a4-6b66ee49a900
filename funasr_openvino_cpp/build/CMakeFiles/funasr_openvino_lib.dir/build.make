# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /media/DataWork/code/sensevoice0825/funasr_openvino_cpp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build

# Include any dependencies generated for this target.
include CMakeFiles/funasr_openvino_lib.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/funasr_openvino_lib.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/funasr_openvino_lib.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/funasr_openvino_lib.dir/flags.make

CMakeFiles/funasr_openvino_lib.dir/src/common.cpp.o: CMakeFiles/funasr_openvino_lib.dir/flags.make
CMakeFiles/funasr_openvino_lib.dir/src/common.cpp.o: ../src/common.cpp
CMakeFiles/funasr_openvino_lib.dir/src/common.cpp.o: CMakeFiles/funasr_openvino_lib.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/funasr_openvino_lib.dir/src/common.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/funasr_openvino_lib.dir/src/common.cpp.o -MF CMakeFiles/funasr_openvino_lib.dir/src/common.cpp.o.d -o CMakeFiles/funasr_openvino_lib.dir/src/common.cpp.o -c /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/src/common.cpp

CMakeFiles/funasr_openvino_lib.dir/src/common.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/funasr_openvino_lib.dir/src/common.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/src/common.cpp > CMakeFiles/funasr_openvino_lib.dir/src/common.cpp.i

CMakeFiles/funasr_openvino_lib.dir/src/common.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/funasr_openvino_lib.dir/src/common.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/src/common.cpp -o CMakeFiles/funasr_openvino_lib.dir/src/common.cpp.s

CMakeFiles/funasr_openvino_lib.dir/src/fsmn_vad_openvino.cpp.o: CMakeFiles/funasr_openvino_lib.dir/flags.make
CMakeFiles/funasr_openvino_lib.dir/src/fsmn_vad_openvino.cpp.o: ../src/fsmn_vad_openvino.cpp
CMakeFiles/funasr_openvino_lib.dir/src/fsmn_vad_openvino.cpp.o: CMakeFiles/funasr_openvino_lib.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/funasr_openvino_lib.dir/src/fsmn_vad_openvino.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/funasr_openvino_lib.dir/src/fsmn_vad_openvino.cpp.o -MF CMakeFiles/funasr_openvino_lib.dir/src/fsmn_vad_openvino.cpp.o.d -o CMakeFiles/funasr_openvino_lib.dir/src/fsmn_vad_openvino.cpp.o -c /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/src/fsmn_vad_openvino.cpp

CMakeFiles/funasr_openvino_lib.dir/src/fsmn_vad_openvino.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/funasr_openvino_lib.dir/src/fsmn_vad_openvino.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/src/fsmn_vad_openvino.cpp > CMakeFiles/funasr_openvino_lib.dir/src/fsmn_vad_openvino.cpp.i

CMakeFiles/funasr_openvino_lib.dir/src/fsmn_vad_openvino.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/funasr_openvino_lib.dir/src/fsmn_vad_openvino.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/src/fsmn_vad_openvino.cpp -o CMakeFiles/funasr_openvino_lib.dir/src/fsmn_vad_openvino.cpp.s

CMakeFiles/funasr_openvino_lib.dir/src/sensevoice_openvino.cpp.o: CMakeFiles/funasr_openvino_lib.dir/flags.make
CMakeFiles/funasr_openvino_lib.dir/src/sensevoice_openvino.cpp.o: ../src/sensevoice_openvino.cpp
CMakeFiles/funasr_openvino_lib.dir/src/sensevoice_openvino.cpp.o: CMakeFiles/funasr_openvino_lib.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/funasr_openvino_lib.dir/src/sensevoice_openvino.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/funasr_openvino_lib.dir/src/sensevoice_openvino.cpp.o -MF CMakeFiles/funasr_openvino_lib.dir/src/sensevoice_openvino.cpp.o.d -o CMakeFiles/funasr_openvino_lib.dir/src/sensevoice_openvino.cpp.o -c /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/src/sensevoice_openvino.cpp

CMakeFiles/funasr_openvino_lib.dir/src/sensevoice_openvino.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/funasr_openvino_lib.dir/src/sensevoice_openvino.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/src/sensevoice_openvino.cpp > CMakeFiles/funasr_openvino_lib.dir/src/sensevoice_openvino.cpp.i

CMakeFiles/funasr_openvino_lib.dir/src/sensevoice_openvino.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/funasr_openvino_lib.dir/src/sensevoice_openvino.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/src/sensevoice_openvino.cpp -o CMakeFiles/funasr_openvino_lib.dir/src/sensevoice_openvino.cpp.s

CMakeFiles/funasr_openvino_lib.dir/src/funasr_openvino_system.cpp.o: CMakeFiles/funasr_openvino_lib.dir/flags.make
CMakeFiles/funasr_openvino_lib.dir/src/funasr_openvino_system.cpp.o: ../src/funasr_openvino_system.cpp
CMakeFiles/funasr_openvino_lib.dir/src/funasr_openvino_system.cpp.o: CMakeFiles/funasr_openvino_lib.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/funasr_openvino_lib.dir/src/funasr_openvino_system.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/funasr_openvino_lib.dir/src/funasr_openvino_system.cpp.o -MF CMakeFiles/funasr_openvino_lib.dir/src/funasr_openvino_system.cpp.o.d -o CMakeFiles/funasr_openvino_lib.dir/src/funasr_openvino_system.cpp.o -c /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/src/funasr_openvino_system.cpp

CMakeFiles/funasr_openvino_lib.dir/src/funasr_openvino_system.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/funasr_openvino_lib.dir/src/funasr_openvino_system.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/src/funasr_openvino_system.cpp > CMakeFiles/funasr_openvino_lib.dir/src/funasr_openvino_system.cpp.i

CMakeFiles/funasr_openvino_lib.dir/src/funasr_openvino_system.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/funasr_openvino_lib.dir/src/funasr_openvino_system.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/src/funasr_openvino_system.cpp -o CMakeFiles/funasr_openvino_lib.dir/src/funasr_openvino_system.cpp.s

# Object files for target funasr_openvino_lib
funasr_openvino_lib_OBJECTS = \
"CMakeFiles/funasr_openvino_lib.dir/src/common.cpp.o" \
"CMakeFiles/funasr_openvino_lib.dir/src/fsmn_vad_openvino.cpp.o" \
"CMakeFiles/funasr_openvino_lib.dir/src/sensevoice_openvino.cpp.o" \
"CMakeFiles/funasr_openvino_lib.dir/src/funasr_openvino_system.cpp.o"

# External object files for target funasr_openvino_lib
funasr_openvino_lib_EXTERNAL_OBJECTS =

libfunasr_openvino_lib.a: CMakeFiles/funasr_openvino_lib.dir/src/common.cpp.o
libfunasr_openvino_lib.a: CMakeFiles/funasr_openvino_lib.dir/src/fsmn_vad_openvino.cpp.o
libfunasr_openvino_lib.a: CMakeFiles/funasr_openvino_lib.dir/src/sensevoice_openvino.cpp.o
libfunasr_openvino_lib.a: CMakeFiles/funasr_openvino_lib.dir/src/funasr_openvino_system.cpp.o
libfunasr_openvino_lib.a: CMakeFiles/funasr_openvino_lib.dir/build.make
libfunasr_openvino_lib.a: CMakeFiles/funasr_openvino_lib.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Linking CXX static library libfunasr_openvino_lib.a"
	$(CMAKE_COMMAND) -P CMakeFiles/funasr_openvino_lib.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/funasr_openvino_lib.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/funasr_openvino_lib.dir/build: libfunasr_openvino_lib.a
.PHONY : CMakeFiles/funasr_openvino_lib.dir/build

CMakeFiles/funasr_openvino_lib.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/funasr_openvino_lib.dir/cmake_clean.cmake
.PHONY : CMakeFiles/funasr_openvino_lib.dir/clean

CMakeFiles/funasr_openvino_lib.dir/depend:
	cd /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /media/DataWork/code/sensevoice0825/funasr_openvino_cpp /media/DataWork/code/sensevoice0825/funasr_openvino_cpp /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build/CMakeFiles/funasr_openvino_lib.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/funasr_openvino_lib.dir/depend

