# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

CMakeFiles/funasr_openvino_main.dir/bin/funasr_openvino_main.cpp.o
 /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/bin/funasr_openvino_main.cpp
 /usr/include/stdc-predef.h
 /usr/include/c++/11/iostream
 /usr/include/x86_64-linux-gnu/c++/11/bits/c++config.h
 /usr/include/x86_64-linux-gnu/c++/11/bits/os_defines.h
 /usr/include/features.h
 /usr/include/features-time64.h
 /usr/include/x86_64-linux-gnu/bits/wordsize.h
 /usr/include/x86_64-linux-gnu/bits/timesize.h
 /usr/include/x86_64-linux-gnu/sys/cdefs.h
 /usr/include/x86_64-linux-gnu/bits/long-double.h
 /usr/include/x86_64-linux-gnu/gnu/stubs.h
 /usr/include/x86_64-linux-gnu/gnu/stubs-64.h
 /usr/include/x86_64-linux-gnu/c++/11/bits/cpu_defines.h
 /usr/include/c++/11/pstl/pstl_config.h
 /usr/include/c++/11/ostream
 /usr/include/c++/11/ios
 /usr/include/c++/11/iosfwd
 /usr/include/c++/11/bits/stringfwd.h
 /usr/include/c++/11/bits/memoryfwd.h
 /usr/include/c++/11/bits/postypes.h
 /usr/include/c++/11/cwchar
 /usr/include/wchar.h
 /usr/include/x86_64-linux-gnu/bits/libc-header-start.h
 /usr/include/x86_64-linux-gnu/bits/floatn.h
 /usr/include/x86_64-linux-gnu/bits/floatn-common.h
 /usr/lib/gcc/x86_64-linux-gnu/11/include/stddef.h
 /usr/lib/gcc/x86_64-linux-gnu/11/include/stdarg.h
 /usr/include/x86_64-linux-gnu/bits/wchar.h
 /usr/include/x86_64-linux-gnu/bits/types/wint_t.h
 /usr/include/x86_64-linux-gnu/bits/types/mbstate_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/locale_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__locale_t.h
 /usr/include/x86_64-linux-gnu/bits/wchar2.h
 /usr/include/c++/11/exception
 /usr/include/c++/11/bits/exception.h
 /usr/include/c++/11/bits/exception_ptr.h
 /usr/include/c++/11/bits/exception_defines.h
 /usr/include/c++/11/bits/cxxabi_init_exception.h
 /usr/include/c++/11/typeinfo
 /usr/include/c++/11/bits/hash_bytes.h
 /usr/include/c++/11/new
 /usr/include/c++/11/bits/move.h
 /usr/include/c++/11/type_traits
 /usr/include/c++/11/bits/nested_exception.h
 /usr/include/c++/11/bits/char_traits.h
 /usr/include/c++/11/bits/stl_algobase.h
 /usr/include/c++/11/bits/functexcept.h
 /usr/include/c++/11/bits/cpp_type_traits.h
 /usr/include/c++/11/ext/type_traits.h
 /usr/include/c++/11/ext/numeric_traits.h
 /usr/include/c++/11/bits/stl_pair.h
 /usr/include/c++/11/bits/stl_iterator_base_types.h
 /usr/include/c++/11/bits/stl_iterator_base_funcs.h
 /usr/include/c++/11/bits/concept_check.h
 /usr/include/c++/11/debug/assertions.h
 /usr/include/c++/11/bits/stl_iterator.h
 /usr/include/c++/11/bits/ptr_traits.h
 /usr/include/c++/11/debug/debug.h
 /usr/include/c++/11/bits/predefined_ops.h
 /usr/include/c++/11/cstdint
 /usr/lib/gcc/x86_64-linux-gnu/11/include/stdint.h
 /usr/include/stdint.h
 /usr/include/x86_64-linux-gnu/bits/types.h
 /usr/include/x86_64-linux-gnu/bits/typesizes.h
 /usr/include/x86_64-linux-gnu/bits/time64.h
 /usr/include/x86_64-linux-gnu/bits/stdint-intn.h
 /usr/include/x86_64-linux-gnu/bits/stdint-uintn.h
 /usr/include/c++/11/bits/localefwd.h
 /usr/include/x86_64-linux-gnu/c++/11/bits/c++locale.h
 /usr/include/c++/11/clocale
 /usr/include/locale.h
 /usr/include/x86_64-linux-gnu/bits/locale.h
 /usr/include/c++/11/cctype
 /usr/include/ctype.h
 /usr/include/x86_64-linux-gnu/bits/endian.h
 /usr/include/x86_64-linux-gnu/bits/endianness.h
 /usr/include/c++/11/bits/ios_base.h
 /usr/include/c++/11/ext/atomicity.h
 /usr/include/x86_64-linux-gnu/c++/11/bits/gthr.h
 /usr/include/x86_64-linux-gnu/c++/11/bits/gthr-default.h
 /usr/include/pthread.h
 /usr/include/sched.h
 /usr/include/x86_64-linux-gnu/bits/types/time_t.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h
 /usr/include/x86_64-linux-gnu/bits/sched.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_sched_param.h
 /usr/include/x86_64-linux-gnu/bits/cpu-set.h
 /usr/include/time.h
 /usr/include/x86_64-linux-gnu/bits/time.h
 /usr/include/x86_64-linux-gnu/bits/timex.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_timeval.h
 /usr/include/x86_64-linux-gnu/bits/types/clock_t.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_tm.h
 /usr/include/x86_64-linux-gnu/bits/types/clockid_t.h
 /usr/include/x86_64-linux-gnu/bits/types/timer_t.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes.h
 /usr/include/x86_64-linux-gnu/bits/thread-shared-types.h
 /usr/include/x86_64-linux-gnu/bits/pthreadtypes-arch.h
 /usr/include/x86_64-linux-gnu/bits/atomic_wide_counter.h
 /usr/include/x86_64-linux-gnu/bits/struct_mutex.h
 /usr/include/x86_64-linux-gnu/bits/struct_rwlock.h
 /usr/include/x86_64-linux-gnu/bits/setjmp.h
 /usr/include/x86_64-linux-gnu/bits/types/__sigset_t.h
 /usr/include/x86_64-linux-gnu/bits/types/struct___jmp_buf_tag.h
 /usr/include/x86_64-linux-gnu/bits/pthread_stack_min-dynamic.h
 /usr/include/x86_64-linux-gnu/c++/11/bits/atomic_word.h
 /usr/include/x86_64-linux-gnu/sys/single_threaded.h
 /usr/include/c++/11/bits/locale_classes.h
 /usr/include/c++/11/string
 /usr/include/c++/11/bits/allocator.h
 /usr/include/x86_64-linux-gnu/c++/11/bits/c++allocator.h
 /usr/include/c++/11/ext/new_allocator.h
 /usr/include/c++/11/bits/ostream_insert.h
 /usr/include/c++/11/bits/cxxabi_forced.h
 /usr/include/c++/11/bits/stl_function.h
 /usr/include/c++/11/backward/binders.h
 /usr/include/c++/11/bits/range_access.h
 /usr/include/c++/11/initializer_list
 /usr/include/c++/11/bits/basic_string.h
 /usr/include/c++/11/ext/alloc_traits.h
 /usr/include/c++/11/bits/alloc_traits.h
 /usr/include/c++/11/bits/stl_construct.h
 /usr/include/c++/11/string_view
 /usr/include/c++/11/bits/functional_hash.h
 /usr/include/c++/11/bits/string_view.tcc
 /usr/include/c++/11/ext/string_conversions.h
 /usr/include/c++/11/cstdlib
 /usr/include/stdlib.h
 /usr/include/x86_64-linux-gnu/bits/waitflags.h
 /usr/include/x86_64-linux-gnu/bits/waitstatus.h
 /usr/include/x86_64-linux-gnu/sys/types.h
 /usr/include/endian.h
 /usr/include/x86_64-linux-gnu/bits/byteswap.h
 /usr/include/x86_64-linux-gnu/bits/uintn-identity.h
 /usr/include/x86_64-linux-gnu/sys/select.h
 /usr/include/x86_64-linux-gnu/bits/select.h
 /usr/include/x86_64-linux-gnu/bits/types/sigset_t.h
 /usr/include/x86_64-linux-gnu/bits/select2.h
 /usr/include/alloca.h
 /usr/include/x86_64-linux-gnu/bits/stdlib-bsearch.h
 /usr/include/x86_64-linux-gnu/bits/stdlib-float.h
 /usr/include/x86_64-linux-gnu/bits/stdlib.h
 /usr/include/c++/11/bits/std_abs.h
 /usr/include/c++/11/cstdio
 /usr/include/stdio.h
 /usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h
 /usr/include/x86_64-linux-gnu/bits/stdio_lim.h
 /usr/include/x86_64-linux-gnu/bits/stdio.h
 /usr/include/x86_64-linux-gnu/bits/stdio2.h
 /usr/include/c++/11/cerrno
 /usr/include/errno.h
 /usr/include/x86_64-linux-gnu/bits/errno.h
 /usr/include/linux/errno.h
 /usr/include/x86_64-linux-gnu/asm/errno.h
 /usr/include/asm-generic/errno.h
 /usr/include/asm-generic/errno-base.h
 /usr/include/x86_64-linux-gnu/bits/types/error_t.h
 /usr/include/c++/11/bits/charconv.h
 /usr/include/c++/11/bits/basic_string.tcc
 /usr/include/c++/11/bits/locale_classes.tcc
 /usr/include/c++/11/system_error
 /usr/include/x86_64-linux-gnu/c++/11/bits/error_constants.h
 /usr/include/c++/11/stdexcept
 /usr/include/c++/11/streambuf
 /usr/include/c++/11/bits/streambuf.tcc
 /usr/include/c++/11/bits/basic_ios.h
 /usr/include/c++/11/bits/locale_facets.h
 /usr/include/c++/11/cwctype
 /usr/include/wctype.h
 /usr/include/x86_64-linux-gnu/bits/wctype-wchar.h
 /usr/include/x86_64-linux-gnu/c++/11/bits/ctype_base.h
 /usr/include/c++/11/bits/streambuf_iterator.h
 /usr/include/x86_64-linux-gnu/c++/11/bits/ctype_inline.h
 /usr/include/c++/11/bits/locale_facets.tcc
 /usr/include/c++/11/bits/basic_ios.tcc
 /usr/include/c++/11/bits/ostream.tcc
 /usr/include/c++/11/istream
 /usr/include/c++/11/bits/istream.tcc
 /usr/include/c++/11/chrono
 /usr/include/c++/11/ratio
 /usr/include/c++/11/limits
 /usr/include/c++/11/ctime
 /usr/include/c++/11/bits/parse_numbers.h
 /usr/include/getopt.h
 /usr/include/x86_64-linux-gnu/bits/getopt_core.h
 /usr/include/x86_64-linux-gnu/bits/getopt_ext.h
 /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/include/funasr_openvino_system.h
 /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/include/common.h
 /usr/include/c++/11/vector
 /usr/include/c++/11/bits/stl_uninitialized.h
 /usr/include/c++/11/bits/stl_vector.h
 /usr/include/c++/11/bits/stl_bvector.h
 /usr/include/c++/11/bits/vector.tcc
 /usr/include/c++/11/memory
 /usr/include/c++/11/bits/stl_tempbuf.h
 /usr/include/c++/11/bits/stl_raw_storage_iter.h
 /usr/include/c++/11/bits/align.h
 /usr/include/c++/11/bit
 /usr/include/c++/11/bits/uses_allocator.h
 /usr/include/c++/11/bits/unique_ptr.h
 /usr/include/c++/11/utility
 /usr/include/c++/11/bits/stl_relops.h
 /usr/include/c++/11/tuple
 /usr/include/c++/11/array
 /usr/include/c++/11/bits/invoke.h
 /usr/include/c++/11/bits/shared_ptr.h
 /usr/include/c++/11/bits/shared_ptr_base.h
 /usr/include/c++/11/bits/allocated_ptr.h
 /usr/include/c++/11/bits/refwrap.h
 /usr/include/c++/11/ext/aligned_buffer.h
 /usr/include/c++/11/ext/concurrence.h
 /usr/include/c++/11/bits/shared_ptr_atomic.h
 /usr/include/c++/11/bits/atomic_base.h
 /usr/include/c++/11/bits/atomic_lockfree_defines.h
 /usr/include/c++/11/backward/auto_ptr.h
 /usr/include/c++/11/pstl/glue_memory_defs.h
 /usr/include/c++/11/pstl/execution_defs.h
 /usr/include/c++/11/map
 /usr/include/c++/11/bits/stl_tree.h
 /usr/include/c++/11/bits/node_handle.h
 /usr/include/c++/11/bits/stl_map.h
 /usr/include/c++/11/bits/stl_multimap.h
 /usr/include/c++/11/bits/erase_if.h
 /usr/include/c++/11/fstream
 /usr/include/c++/11/bits/codecvt.h
 /usr/include/x86_64-linux-gnu/c++/11/bits/basic_file.h
 /usr/include/x86_64-linux-gnu/c++/11/bits/c++io.h
 /usr/include/c++/11/bits/fstream.tcc
 /usr/include/c++/11/sstream
 /usr/include/c++/11/bits/sstream.tcc
 /usr/include/c++/11/algorithm
 /usr/include/c++/11/bits/stl_algo.h
 /usr/include/c++/11/bits/algorithmfwd.h
 /usr/include/c++/11/bits/stl_heap.h
 /usr/include/c++/11/bits/uniform_int_dist.h
 /usr/include/c++/11/pstl/glue_algorithm_defs.h
 /usr/include/c++/11/functional
 /usr/include/c++/11/bits/std_function.h
 /usr/include/c++/11/unordered_map
 /usr/include/c++/11/bits/hashtable.h
 /usr/include/c++/11/bits/hashtable_policy.h
 /usr/include/c++/11/bits/enable_special_members.h
 /usr/include/c++/11/bits/unordered_map.h
 /usr/include/c++/11/numeric
 /usr/include/c++/11/bits/stl_numeric.h
 /usr/include/c++/11/pstl/glue_numeric_defs.h
 /usr/include/c++/11/cmath
 /usr/include/math.h
 /usr/include/x86_64-linux-gnu/bits/math-vector.h
 /usr/include/x86_64-linux-gnu/bits/libm-simd-decl-stubs.h
 /usr/include/x86_64-linux-gnu/bits/flt-eval-method.h
 /usr/include/x86_64-linux-gnu/bits/fp-logb.h
 /usr/include/x86_64-linux-gnu/bits/fp-fast.h
 /usr/include/x86_64-linux-gnu/bits/mathcalls-helper-functions.h
 /usr/include/x86_64-linux-gnu/bits/mathcalls.h
 /usr/include/x86_64-linux-gnu/bits/mathcalls-narrow.h
 /usr/include/x86_64-linux-gnu/bits/iscanonical.h
 /usr/include/c++/11/bits/specfun.h
 /usr/include/c++/11/tr1/gamma.tcc
 /usr/include/c++/11/tr1/special_function_util.h
 /usr/include/c++/11/tr1/bessel_function.tcc
 /usr/include/c++/11/tr1/beta_function.tcc
 /usr/include/c++/11/tr1/ell_integral.tcc
 /usr/include/c++/11/tr1/exp_integral.tcc
 /usr/include/c++/11/tr1/hypergeometric.tcc
 /usr/include/c++/11/tr1/legendre_function.tcc
 /usr/include/c++/11/tr1/modified_bessel_func.tcc
 /usr/include/c++/11/tr1/poly_hermite.tcc
 /usr/include/c++/11/tr1/poly_laguerre.tcc
 /usr/include/c++/11/tr1/riemann_zeta.tcc
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/openvino.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/core.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/attribute_adapter.hpp
 /usr/include/c++/11/set
 /usr/include/c++/11/bits/stl_set.h
 /usr/include/c++/11/bits/stl_multiset.h
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/any.hpp
 /usr/include/c++/11/typeindex
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/attribute_visitor.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/type.hpp
 /usr/include/c++/11/cstring
 /usr/include/string.h
 /usr/include/strings.h
 /usr/include/x86_64-linux-gnu/bits/strings_fortified.h
 /usr/include/x86_64-linux-gnu/bits/string_fortified.h
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/core_visibility.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/visibility.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/except.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/deprecated.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/runtime_attribute.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/node_vector.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/rtti.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/enum_names.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/axis_set.hpp
 /usr/include/c++/11/cstddef
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/axis_vector.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/coordinate.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/shape.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/strides.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/coordinate_diff.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/dimension.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/interval.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/symbol.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/enum_mask.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/graph_util.hpp
 /usr/include/c++/11/deque
 /usr/include/c++/11/bits/stl_deque.h
 /usr/include/c++/11/bits/deque.tcc
 /usr/include/c++/11/filesystem
 /usr/include/c++/11/bits/fs_fwd.h
 /usr/include/c++/11/bits/fs_path.h
 /usr/include/c++/11/locale
 /usr/include/c++/11/bits/locale_facets_nonio.h
 /usr/include/x86_64-linux-gnu/c++/11/bits/time_members.h
 /usr/include/x86_64-linux-gnu/c++/11/bits/messages_members.h
 /usr/include/libintl.h
 /usr/include/c++/11/bits/locale_facets_nonio.tcc
 /usr/include/c++/11/bits/locale_conv.h
 /usr/include/c++/11/iomanip
 /usr/include/c++/11/bits/quoted_string.h
 /usr/include/c++/11/codecvt
 /usr/include/c++/11/bits/fs_dir.h
 /usr/include/c++/11/bits/fs_ops.h
 /usr/include/c++/11/list
 /usr/include/c++/11/bits/stl_list.h
 /usr/include/c++/11/bits/list.tcc
 /usr/include/c++/11/stack
 /usr/include/c++/11/bits/stl_stack.h
 /usr/include/c++/11/unordered_set
 /usr/include/c++/11/bits/unordered_set.h
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/model.hpp
 /usr/include/c++/11/atomic
 /usr/include/c++/11/mutex
 /usr/include/c++/11/bits/std_mutex.h
 /usr/include/c++/11/bits/unique_lock.h
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/node.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/descriptor/input.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/descriptor/tensor.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/partial_shape.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/rank.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/op/util/attr_types.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/type/element_type.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/type/bfloat16.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/type/float16.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/type/float4_e2m1.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/type/float8_e4m3.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/type/float8_e5m2.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/type/float8_e8m0.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/runtime/tensor.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/runtime/allocator.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/descriptor/output.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/node_output.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/node_input.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/op/util/variable.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/op/util/variable_value.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/op/assign.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/op/util/assign_base.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/op/sink.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/op/op.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/op/util/variable_extension.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/op/parameter.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/layout.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/op/read_value.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/op/util/read_value_base.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/op/result.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/pass/serialize.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/opsets/opset.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/pass/pass.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/pass/pass_config.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/rt_info.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/version.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/preprocess/color_format.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/preprocess/input_info.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/preprocess/input_model_info.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/preprocess/input_tensor_info.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/preprocess/preprocess_steps.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/preprocess/padding_mode.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/preprocess/resize_algorithm.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/preprocess/output_info.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/preprocess/output_model_info.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/preprocess/output_tensor_info.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/preprocess/postprocess_steps.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/preprocess/pre_post_process.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/type/element_type_traits.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/runtime/runtime.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/runtime/core.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/extension.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/core/op_extension.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/runtime/common.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/runtime/compiled_model.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/runtime/infer_request.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/runtime/profiling_info.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/runtime/variable_state.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/runtime/properties.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/runtime/remote_context.hpp
 /opt/intel/openvino_genai_2025.2.0/runtime/include/openvino/runtime/remote_tensor.hpp
 /usr/include/sndfile.h
 /usr/include/x86_64-linux-gnu/libavformat/avformat.h
 /usr/include/x86_64-linux-gnu/libavcodec/avcodec.h
 /usr/include/x86_64-linux-gnu/libavutil/samplefmt.h
 /usr/include/x86_64-linux-gnu/libavutil/avutil.h
 /usr/include/x86_64-linux-gnu/libavutil/common.h
 /usr/include/inttypes.h
 /usr/lib/gcc/x86_64-linux-gnu/11/include/limits.h
 /usr/lib/gcc/x86_64-linux-gnu/11/include/syslimits.h
 /usr/include/limits.h
 /usr/include/x86_64-linux-gnu/bits/posix1_lim.h
 /usr/include/x86_64-linux-gnu/bits/local_lim.h
 /usr/include/linux/limits.h
 /usr/include/x86_64-linux-gnu/bits/posix2_lim.h
 /usr/include/x86_64-linux-gnu/bits/xopen_lim.h
 /usr/include/x86_64-linux-gnu/bits/uio_lim.h
 /usr/include/c++/11/math.h
 /usr/include/c++/11/stdlib.h
 /usr/include/x86_64-linux-gnu/libavutil/attributes.h
 /usr/include/x86_64-linux-gnu/libavutil/macros.h
 /usr/include/x86_64-linux-gnu/libavutil/version.h
 /usr/include/x86_64-linux-gnu/libavutil/avconfig.h
 /usr/include/x86_64-linux-gnu/libavutil/mem.h
 /usr/include/x86_64-linux-gnu/libavutil/error.h
 /usr/include/x86_64-linux-gnu/libavutil/rational.h
 /usr/include/x86_64-linux-gnu/libavutil/mathematics.h
 /usr/include/x86_64-linux-gnu/libavutil/intfloat.h
 /usr/include/x86_64-linux-gnu/libavutil/log.h
 /usr/include/x86_64-linux-gnu/libavutil/pixfmt.h
 /usr/include/x86_64-linux-gnu/libavutil/attributes.h
 /usr/include/x86_64-linux-gnu/libavutil/avutil.h
 /usr/include/x86_64-linux-gnu/libavutil/buffer.h
 /usr/include/x86_64-linux-gnu/libavutil/cpu.h
 /usr/include/x86_64-linux-gnu/libavutil/channel_layout.h
 /usr/include/x86_64-linux-gnu/libavutil/dict.h
 /usr/include/x86_64-linux-gnu/libavutil/frame.h
 /usr/include/x86_64-linux-gnu/libavutil/buffer.h
 /usr/include/x86_64-linux-gnu/libavutil/dict.h
 /usr/include/x86_64-linux-gnu/libavutil/samplefmt.h
 /usr/include/x86_64-linux-gnu/libavutil/hwcontext.h
 /usr/include/x86_64-linux-gnu/libavutil/frame.h
 /usr/include/x86_64-linux-gnu/libavutil/log.h
 /usr/include/x86_64-linux-gnu/libavutil/pixfmt.h
 /usr/include/x86_64-linux-gnu/libavutil/rational.h
 /usr/include/x86_64-linux-gnu/libavcodec/bsf.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec_id.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec_par.h
 /usr/include/x86_64-linux-gnu/libavcodec/packet.h
 /usr/include/x86_64-linux-gnu/libavcodec/version.h
 /usr/include/x86_64-linux-gnu/libavutil/version.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec_id.h
 /usr/include/x86_64-linux-gnu/libavcodec/codec_desc.h
 /usr/include/x86_64-linux-gnu/libavcodec/version.h
 /usr/include/x86_64-linux-gnu/libavformat/avio.h
 /usr/include/x86_64-linux-gnu/libavutil/common.h
 /usr/include/x86_64-linux-gnu/libavformat/version.h
 /usr/include/x86_64-linux-gnu/libswresample/swresample.h
 /usr/include/x86_64-linux-gnu/libswresample/version.h
 /usr/include/yaml-cpp/yaml.h
 /usr/include/yaml-cpp/parser.h
 /usr/include/yaml-cpp/dll.h
 /usr/include/yaml-cpp/emitter.h
 /usr/include/yaml-cpp/binary.h
 /usr/include/yaml-cpp/emitterdef.h
 /usr/include/yaml-cpp/emittermanip.h
 /usr/include/yaml-cpp/null.h
 /usr/include/yaml-cpp/ostream_wrapper.h
 /usr/include/yaml-cpp/emitterstyle.h
 /usr/include/yaml-cpp/stlemitter.h
 /usr/include/yaml-cpp/exceptions.h
 /usr/include/yaml-cpp/mark.h
 /usr/include/yaml-cpp/noexcept.h
 /usr/include/yaml-cpp/traits.h
 /usr/include/yaml-cpp/node/node.h
 /usr/include/yaml-cpp/node/detail/iterator_fwd.h
 /usr/include/yaml-cpp/node/ptr.h
 /usr/include/yaml-cpp/node/type.h
 /usr/include/yaml-cpp/node/impl.h
 /usr/include/yaml-cpp/node/detail/memory.h
 /usr/include/yaml-cpp/node/detail/node.h
 /usr/include/yaml-cpp/node/detail/node_ref.h
 /usr/include/yaml-cpp/node/detail/node_data.h
 /usr/include/yaml-cpp/node/detail/node_iterator.h
 /usr/include/c++/11/iterator
 /usr/include/c++/11/bits/stream_iterator.h
 /usr/include/yaml-cpp/node/iterator.h
 /usr/include/yaml-cpp/node/detail/iterator.h
 /usr/include/yaml-cpp/node/convert.h
 /usr/include/yaml-cpp/node/detail/impl.h
 /usr/include/yaml-cpp/node/parse.h
 /usr/include/yaml-cpp/node/emit.h
 /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/include/fsmn_vad_openvino.h
 /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/include/sensevoice_openvino.h
 /usr/include/sentencepiece_processor.h

