# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.22

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /media/DataWork/code/sensevoice0825/funasr_openvino_cpp

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build

# Include any dependencies generated for this target.
include CMakeFiles/funasr_openvino_main.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/funasr_openvino_main.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/funasr_openvino_main.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/funasr_openvino_main.dir/flags.make

CMakeFiles/funasr_openvino_main.dir/bin/funasr_openvino_main.cpp.o: CMakeFiles/funasr_openvino_main.dir/flags.make
CMakeFiles/funasr_openvino_main.dir/bin/funasr_openvino_main.cpp.o: ../bin/funasr_openvino_main.cpp
CMakeFiles/funasr_openvino_main.dir/bin/funasr_openvino_main.cpp.o: CMakeFiles/funasr_openvino_main.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/funasr_openvino_main.dir/bin/funasr_openvino_main.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/funasr_openvino_main.dir/bin/funasr_openvino_main.cpp.o -MF CMakeFiles/funasr_openvino_main.dir/bin/funasr_openvino_main.cpp.o.d -o CMakeFiles/funasr_openvino_main.dir/bin/funasr_openvino_main.cpp.o -c /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/bin/funasr_openvino_main.cpp

CMakeFiles/funasr_openvino_main.dir/bin/funasr_openvino_main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/funasr_openvino_main.dir/bin/funasr_openvino_main.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/bin/funasr_openvino_main.cpp > CMakeFiles/funasr_openvino_main.dir/bin/funasr_openvino_main.cpp.i

CMakeFiles/funasr_openvino_main.dir/bin/funasr_openvino_main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/funasr_openvino_main.dir/bin/funasr_openvino_main.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/bin/funasr_openvino_main.cpp -o CMakeFiles/funasr_openvino_main.dir/bin/funasr_openvino_main.cpp.s

# Object files for target funasr_openvino_main
funasr_openvino_main_OBJECTS = \
"CMakeFiles/funasr_openvino_main.dir/bin/funasr_openvino_main.cpp.o"

# External object files for target funasr_openvino_main
funasr_openvino_main_EXTERNAL_OBJECTS =

funasr_openvino_main: CMakeFiles/funasr_openvino_main.dir/bin/funasr_openvino_main.cpp.o
funasr_openvino_main: CMakeFiles/funasr_openvino_main.dir/build.make
funasr_openvino_main: libfunasr_openvino_lib.a
funasr_openvino_main: /opt/intel/openvino_genai_2025.2.0/runtime/lib/intel64/libopenvino.so.2025.2.0
funasr_openvino_main: CMakeFiles/funasr_openvino_main.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable funasr_openvino_main"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/funasr_openvino_main.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/funasr_openvino_main.dir/build: funasr_openvino_main
.PHONY : CMakeFiles/funasr_openvino_main.dir/build

CMakeFiles/funasr_openvino_main.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/funasr_openvino_main.dir/cmake_clean.cmake
.PHONY : CMakeFiles/funasr_openvino_main.dir/clean

CMakeFiles/funasr_openvino_main.dir/depend:
	cd /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /media/DataWork/code/sensevoice0825/funasr_openvino_cpp /media/DataWork/code/sensevoice0825/funasr_openvino_cpp /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build /media/DataWork/code/sensevoice0825/funasr_openvino_cpp/build/CMakeFiles/funasr_openvino_main.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/funasr_openvino_main.dir/depend

