/**
 * FunASR OpenVINO C++ Implementation
 * Based on Python code: 9_funasr_openvino_official0827.py
 * 
 * Features:
 * - FSMN-VAD OpenVINO inference
 * - SenseVoice OpenVINO inference  
 * - Complete FunASR official inference_with_vad workflow
 * - Replaces ONNX models with OpenVINO models
 * 
 * Usage:
 *   ./funasr_openvino_main --audio ../data/audio.wav \
 *                          --vad_model ../funasr_models/iic/fsmn_vad_zh-cn_openvino \
 *                          --asr_model ../converted_models/openvino/model_fp16.xml \
 *                          --device CPU
 * 
 * Author: Converted from Python implementation
 * License: MIT
 */

#include <iostream>
#include <string>
#include <chrono>
#include <getopt.h>

#include "../include/funasr_openvino_system.h"

using namespace funasr_openvino;

void PrintUsage(const char* program_name) {
    std::cout << "Usage: " << program_name << " [OPTIONS]\n"
              << "\nOptions:\n"
              << "  --audio PATH          Audio file path (required)\n"
              << "  --vad_model PATH      VAD OpenVINO model directory (required)\n"
              << "  --asr_model PATH      ASR OpenVINO model path (required)\n"
              << "  --device DEVICE       Inference device (default: CPU)\n"
              << "  --skip_vad            Skip VAD detection, process entire audio\n"
              << "  --merge_vad           Enable VAD segment merging\n"
              << "  --merge_length_s N    VAD merge length in seconds (default: 15)\n"
              << "  --max_single_segment_time N  Max single segment time in ms (default: 60000)\n"
              << "  --language LANG       Language setting (default: auto)\n"
              << "  --batch_size_s N      Batch size in seconds (default: 300)\n"
              << "  --batch_size_threshold_s N  Batch threshold in seconds (default: 60)\n"
              << "  --help                Show this help message\n"
              << "\nExample:\n"
              << "  " << program_name << " --audio audio.wav \\\n"
              << "                       --vad_model ../funasr_models/iic/fsmn_vad_zh-cn_openvino \\\n"
              << "                       --asr_model ../converted_models/openvino/model_fp16.xml\n";
}

struct Arguments {
    std::string audio_path;
    std::string vad_model_dir;
    std::string asr_model_path;
    std::string device = "CPU";
    bool skip_vad = false;
    bool merge_vad = false;
    int merge_length_s = 15;
    int max_single_segment_time = 60000;
    std::string language = "auto";
    int batch_size_s = 300;
    int batch_size_threshold_s = 60;
    bool help = false;
};

Arguments ParseArguments(int argc, char* argv[]) {
    Arguments args;
    
    static struct option long_options[] = {
        {"audio", required_argument, 0, 'a'},
        {"vad_model", required_argument, 0, 'v'},
        {"asr_model", required_argument, 0, 's'},
        {"device", required_argument, 0, 'd'},
        {"skip_vad", no_argument, 0, 'k'},
        {"merge_vad", no_argument, 0, 'm'},
        {"merge_length_s", required_argument, 0, 'l'},
        {"max_single_segment_time", required_argument, 0, 't'},
        {"language", required_argument, 0, 'g'},
        {"batch_size_s", required_argument, 0, 'b'},
        {"batch_size_threshold_s", required_argument, 0, 'T'},
        {"help", no_argument, 0, 'h'},
        {0, 0, 0, 0}
    };
    
    int option_index = 0;
    int c;
    
    while ((c = getopt_long(argc, argv, "a:v:s:d:kml:t:g:b:T:h", long_options, &option_index)) != -1) {
        switch (c) {
            case 'a':
                args.audio_path = optarg;
                break;
            case 'v':
                args.vad_model_dir = optarg;
                break;
            case 's':
                args.asr_model_path = optarg;
                break;
            case 'd':
                args.device = optarg;
                break;
            case 'k':
                args.skip_vad = true;
                break;
            case 'm':
                args.merge_vad = true;
                break;
            case 'l':
                args.merge_length_s = std::stoi(optarg);
                break;
            case 't':
                args.max_single_segment_time = std::stoi(optarg);
                break;
            case 'g':
                args.language = optarg;
                break;
            case 'b':
                args.batch_size_s = std::stoi(optarg);
                break;
            case 'T':
                args.batch_size_threshold_s = std::stoi(optarg);
                break;
            case 'h':
                args.help = true;
                break;
            default:
                std::cerr << "Unknown option. Use --help for usage information.\n";
                exit(1);
        }
    }
    
    return args;
}

bool ValidateArguments(const Arguments& args) {
    if (args.help) {
        return false;
    }
    
    if (args.audio_path.empty()) {
        std::cerr << "Error: --audio is required\n";
        return false;
    }
    
    if (args.vad_model_dir.empty()) {
        std::cerr << "Error: --vad_model is required\n";
        return false;
    }
    
    if (args.asr_model_path.empty()) {
        std::cerr << "Error: --asr_model is required\n";
        return false;
    }
    
    return true;
}

int main(int argc, char* argv[]) {
    // Parse command line arguments
    Arguments args = ParseArguments(argc, argv);
    
    if (args.help) {
        PrintUsage(argv[0]);
        return 0;
    }
    
    if (!ValidateArguments(args)) {
        PrintUsage(argv[0]);
        return 1;
    }
    
    try {
        // Record total start time
        Timer total_timer;
        total_timer.Start();
        
        // Initialize system
        std::cout << "🚀 开始初始化系统..." << std::endl;
        Timer init_timer;
        init_timer.Start();
        
        FunASROpenVINOSystem system(
            args.vad_model_dir,
            args.asr_model_path,
            args.device
        );
        
        double init_time = init_timer.ElapsedSeconds();
        
        // Prepare inference configuration
        InferenceConfig config;
        config.skip_vad = args.skip_vad;
        config.merge_vad = args.merge_vad;
        config.merge_length_s = args.merge_length_s;
        config.max_single_segment_time = args.max_single_segment_time;
        config.language = args.language;
        config.batch_size_s = args.batch_size_s;
        config.batch_size_threshold_s = args.batch_size_threshold_s;
        config.device = args.device;

        // Execute inference
        std::cout << "\n🚀 开始推理..." << std::endl;
        Timer inference_timer;
        inference_timer.Start();

        std::vector<ASRResult> results;
        if (args.skip_vad) {
            std::cout << "\n⚡ 跳过VAD检测，直接进行ASR推理" << std::endl;
            results = system.DirectASRInference(args.audio_path, config);
        } else {
            results = system.InferenceWithVAD(args.audio_path, config);
        }
        
        double inference_time = inference_timer.ElapsedSeconds();
        double total_time = total_timer.ElapsedSeconds();
        
        // Output results
        std::cout << "\n============================================================" << std::endl;
        std::cout << "推理结果" << std::endl;
        std::cout << "============================================================" << std::endl;
        
        for (const auto& result : results) {
            std::cout << "Key: " << result.key << std::endl;
            
            // Process text with rich transcription
            std::string original_text = result.text;
            std::string processed_text = utils::RichTranscriptionPostprocess(original_text);
            
            std::cout << "原始文本: " << original_text << std::endl;
            std::cout << "处理文本: " << processed_text << std::endl;
            std::cout << std::string(60, '-') << std::endl;
        }
        
        // Output timing statistics
        std::cout << "\n🏆 总体时间统计:" << std::endl;
        std::cout << "   系统初始化: " << init_time << "秒" << std::endl;
        std::cout << "   推理处理: " << inference_time << "秒" << std::endl;
        std::cout << "   总耗时: " << total_time << "秒" << std::endl;
        
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 错误: " << e.what() << std::endl;
        return 1;
    }
}
