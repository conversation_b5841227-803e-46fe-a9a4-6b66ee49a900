cmake_minimum_required(VERSION 3.16)
project(FunASROpenVINO)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler flags
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -Wall -Wextra")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG")

# Find required packages
find_package(PkgConfig REQUIRED)

# OpenVINO
find_package(OpenVINO REQUIRED)

# libsndfile for audio I/O
pkg_check_modules(SNDFILE REQUIRED sndfile)

# FFmpeg for MP3/MP4 support
pkg_check_modules(FFMPEG REQUIRED libavformat libavcodec libavutil libswresample)

# SentencePiece for tokenization
pkg_check_modules(SENTENCEPIECE REQUIRED sentencepiece)

# yaml-cpp for configuration parsing
find_package(yaml-cpp REQUIRED)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/include)
include_directories(${SNDFILE_INCLUDE_DIRS})
include_directories(${FFMPEG_INCLUDE_DIRS})
include_directories(${SENTENCEPIECE_INCLUDE_DIRS})

# Source files
set(COMMON_SOURCES
    src/common.cpp
    src/fsmn_vad_openvino.cpp
    src/sensevoice_openvino.cpp
    src/funasr_openvino_system.cpp
)

# Create library
add_library(funasr_openvino_lib ${COMMON_SOURCES})

# Link libraries to the library
target_link_libraries(funasr_openvino_lib
    openvino::runtime
    ${SNDFILE_LIBRARIES}
    ${FFMPEG_LIBRARIES}
    ${SENTENCEPIECE_LIBRARIES}
    yaml-cpp
)

# Compiler definitions
target_compile_definitions(funasr_openvino_lib PRIVATE
    ${SNDFILE_CFLAGS_OTHER}
    ${FFMPEG_CFLAGS_OTHER}
    ${SENTENCEPIECE_CFLAGS_OTHER}
)

# Main executable
add_executable(funasr_openvino_main bin/funasr_openvino_main.cpp)

# Link libraries to the executable
target_link_libraries(funasr_openvino_main
    funasr_openvino_lib
    openvino::runtime
    ${SNDFILE_LIBRARIES}
    ${FFMPEG_LIBRARIES}
    ${SENTENCEPIECE_LIBRARIES}
    yaml-cpp
)

# Compiler definitions for executable
target_compile_definitions(funasr_openvino_main PRIVATE
    ${SNDFILE_CFLAGS_OTHER}
    ${FFMPEG_CFLAGS_OTHER}
    ${SENTENCEPIECE_CFLAGS_OTHER}
)

# Installation
install(TARGETS funasr_openvino_main
    RUNTIME DESTINATION bin
)

install(TARGETS funasr_openvino_lib
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

install(DIRECTORY include/
    DESTINATION include/funasr_openvino
    FILES_MATCHING PATTERN "*.h"
)

# Optional: Add tests
option(BUILD_TESTS "Build tests" OFF)
if(BUILD_TESTS)
    enable_testing()
    add_subdirectory(tests)
endif()

# Print configuration summary
message(STATUS "")
message(STATUS "Configuration Summary:")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  OpenVINO found: ${OpenVINO_FOUND}")
message(STATUS "  libsndfile found: ${SNDFILE_FOUND}")
message(STATUS "  yaml-cpp found: ${yaml-cpp_FOUND}")
message(STATUS "")
