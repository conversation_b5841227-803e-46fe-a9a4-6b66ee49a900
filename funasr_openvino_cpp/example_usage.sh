#!/bin/bash

# Example usage script for FunASR OpenVINO C++
# This script demonstrates how to use the compiled executable

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}============================================================${NC}"
echo -e "${BLUE}FunASR OpenVINO C++ Example Usage${NC}"
echo -e "${BLUE}============================================================${NC}"

# Check if executable exists
EXECUTABLE="./build/funasr_openvino_main"
if [ ! -f "$EXECUTABLE" ]; then
    echo -e "${YELLOW}Executable not found. Please build first:${NC}"
    echo "  ./build.sh"
    exit 1
fi

# Default paths (adjust these to your actual model paths)
AUDIO_FILE="../data/像我这样的人-毛不易#hxmnf.mp3"
VAD_MODEL="../funasr_models/iic/fsmn_vad_zh-cn_openvino"
ASR_MODEL="../converted_models/openvino/model_fp16.xml"
DEVICE="CPU"

echo -e "${BLUE}Configuration:${NC}"
echo "  Audio file: $AUDIO_FILE"
echo "  VAD model: $VAD_MODEL"
echo "  ASR model: $ASR_MODEL"
echo "  Device: $DEVICE"
echo ""

# Check if files exist
if [ ! -f "$AUDIO_FILE" ]; then
    echo -e "${YELLOW}Warning: Audio file not found: $AUDIO_FILE${NC}"
    echo "Please adjust the AUDIO_FILE path in this script"
fi

if [ ! -d "$VAD_MODEL" ]; then
    echo -e "${YELLOW}Warning: VAD model directory not found: $VAD_MODEL${NC}"
    echo "Please adjust the VAD_MODEL path in this script"
fi

if [ ! -f "$ASR_MODEL" ]; then
    echo -e "${YELLOW}Warning: ASR model file not found: $ASR_MODEL${NC}"
    echo "Please adjust the ASR_MODEL path in this script"
fi

echo -e "${BLUE}Example 1: Basic usage${NC}"
echo "Command:"
echo "$EXECUTABLE \\"
echo "    --audio \"$AUDIO_FILE\" \\"
echo "    --vad_model \"$VAD_MODEL\" \\"
echo "    --asr_model \"$ASR_MODEL\" \\"
echo "    --device $DEVICE"
echo ""

echo -e "${BLUE}Example 2: With VAD merging${NC}"
echo "Command:"
echo "$EXECUTABLE \\"
echo "    --audio \"$AUDIO_FILE\" \\"
echo "    --vad_model \"$VAD_MODEL\" \\"
echo "    --asr_model \"$ASR_MODEL\" \\"
echo "    --device $DEVICE \\"
echo "    --merge_vad \\"
echo "    --merge_length_s 15"
echo ""

echo -e "${BLUE}Example 3: Full configuration${NC}"
echo "Command:"
echo "$EXECUTABLE \\"
echo "    --audio \"$AUDIO_FILE\" \\"
echo "    --vad_model \"$VAD_MODEL\" \\"
echo "    --asr_model \"$ASR_MODEL\" \\"
echo "    --device $DEVICE \\"
echo "    --merge_vad \\"
echo "    --merge_length_s 15 \\"
echo "    --max_single_segment_time 60000 \\"
echo "    --language auto \\"
echo "    --batch_size_s 300 \\"
echo "    --batch_size_threshold_s 60"
echo ""

echo -e "${BLUE}Example 4: GPU inference (if available)${NC}"
echo "Command:"
echo "$EXECUTABLE \\"
echo "    --audio \"$AUDIO_FILE\" \\"
echo "    --vad_model \"$VAD_MODEL\" \\"
echo "    --asr_model \"$ASR_MODEL\" \\"
echo "    --device GPU"
echo ""

# Ask user if they want to run an example
echo -e "${GREEN}Would you like to run Example 1 (basic usage)? [y/N]${NC}"
read -r response

if [[ "$response" =~ ^[Yy]$ ]]; then
    echo -e "${BLUE}Running Example 1...${NC}"
    echo ""
    
    # Check if all required files exist before running
    missing_files=false
    
    if [ ! -f "$AUDIO_FILE" ]; then
        echo -e "${YELLOW}Error: Audio file not found: $AUDIO_FILE${NC}"
        missing_files=true
    fi
    
    if [ ! -d "$VAD_MODEL" ]; then
        echo -e "${YELLOW}Error: VAD model directory not found: $VAD_MODEL${NC}"
        missing_files=true
    fi
    
    if [ ! -f "$ASR_MODEL" ]; then
        echo -e "${YELLOW}Error: ASR model file not found: $ASR_MODEL${NC}"
        missing_files=true
    fi
    
    if [ "$missing_files" = true ]; then
        echo -e "${YELLOW}Please adjust the file paths in this script and try again.${NC}"
        exit 1
    fi
    
    # Run the command
    $EXECUTABLE \
        --audio "$AUDIO_FILE" \
        --vad_model "$VAD_MODEL" \
        --asr_model "$ASR_MODEL" \
        --device $DEVICE
else
    echo -e "${GREEN}You can copy and modify the commands above to run with your own files.${NC}"
fi

echo ""
echo -e "${BLUE}For more options, run:${NC}"
echo "$EXECUTABLE --help"
echo ""
echo -e "${GREEN}Example usage completed!${NC}"
