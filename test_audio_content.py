#!/usr/bin/env python3
# -*- encoding: utf-8 -*-

import sys
from funasr import AutoModel

def test_audio_content(audio_path):
    """测试音频内容，看看实际包含什么文字"""
    
    print(f"测试音频文件: {audio_path}")
    
    # 使用SenseVoice模型
    model = AutoModel(
        model="iic/SenseVoiceSmall",
        trust_remote_code=True,
        vad_model="fsmn-vad",
        vad_kwargs={"max_single_segment_time": 30000},
        device="cpu",  # 使用CPU避免CUDA问题
    )
    
    # 进行识别
    res = model.generate(
        input=audio_path,
        cache={},
        language="zh",  # 明确指定中文
        use_itn=True,
        batch_size_s=60,
        merge_vad=True,
        merge_length_s=15,
    )
    
    print("识别结果:")
    for i, result in enumerate(res):
        print(f"  段落 {i+1}: {result['text']}")
        if 'timestamp' in result:
            print(f"    时间戳: {result['timestamp']}")
    
    return res

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python test_audio_content.py <audio_file>")
        sys.exit(1)
    
    audio_path = sys.argv[1]
    test_audio_content(audio_path)
