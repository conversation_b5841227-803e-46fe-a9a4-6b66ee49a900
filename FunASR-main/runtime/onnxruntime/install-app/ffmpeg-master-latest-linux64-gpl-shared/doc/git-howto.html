<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Created by GNU Texinfo 6.8, https://www.gnu.org/software/texinfo/ -->
  <head>
    <meta charset="utf-8">
    <title>
      Using Git to develop FFmpeg
    </title>
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="stylesheet" type="text/css" href="bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="style.min.css">
  </head>
  <body>
    <div class="container">
      <h1>
      Using Git to develop FFmpeg
      </h1>
<div align="center">
</div>



<a name="SEC_Top"></a>

<div class="Contents_element" id="SEC_Contents">
<h2 class="contents-heading">Table of Contents</h2>

<div class="contents">

<ul class="no-bullet">
  <li><a id="toc-Introduction" href="#Introduction">1 Introduction</a></li>
  <li><a id="toc-Basics-Usage" href="#Basics-Usage">2 Basics Usage</a>
  <ul class="no-bullet">
    <li><a id="toc-Get-Git" href="#Get-Git">2.1 Get Git</a></li>
    <li><a id="toc-Cloning-the-source-tree" href="#Cloning-the-source-tree">2.2 Cloning the source tree</a></li>
    <li><a id="toc-Updating-the-source-tree-to-the-latest-revision-1" href="#Updating-the-source-tree-to-the-latest-revision-1">2.3 Updating the source tree to the latest revision</a></li>
    <li><a id="toc-Rebasing-your-local-branches" href="#Rebasing-your-local-branches">2.4 Rebasing your local branches</a></li>
    <li><a id="toc-Adding_002fremoving-files_002fdirectories" href="#Adding_002fremoving-files_002fdirectories">2.5 Adding/removing files/directories</a></li>
    <li><a id="toc-Showing-modifications" href="#Showing-modifications">2.6 Showing modifications</a></li>
    <li><a id="toc-Inspecting-the-changelog" href="#Inspecting-the-changelog">2.7 Inspecting the changelog</a></li>
    <li><a id="toc-Checking-source-tree-status" href="#Checking-source-tree-status">2.8 Checking source tree status</a></li>
    <li><a id="toc-Committing" href="#Committing">2.9 Committing</a></li>
    <li><a id="toc-Writing-a-commit-message" href="#Writing-a-commit-message">2.10 Writing a commit message</a></li>
    <li><a id="toc-Preparing-a-patchset" href="#Preparing-a-patchset">2.11 Preparing a patchset</a></li>
    <li><a id="toc-Sending-patches-for-review" href="#Sending-patches-for-review">2.12 Sending patches for review</a></li>
    <li><a id="toc-Renaming_002fmoving_002fcopying-files-or-contents-of-files" href="#Renaming_002fmoving_002fcopying-files-or-contents-of-files">2.13 Renaming/moving/copying files or contents of files</a></li>
  </ul></li>
  <li><a id="toc-Git-configuration" href="#Git-configuration">3 Git configuration</a>
  <ul class="no-bullet">
    <li><a id="toc-Personal-Git-installation" href="#Personal-Git-installation">3.1 Personal Git installation</a></li>
    <li><a id="toc-Repository-configuration" href="#Repository-configuration">3.2 Repository configuration</a></li>
  </ul></li>
  <li><a id="toc-FFmpeg-specific" href="#FFmpeg-specific">4 FFmpeg specific</a>
  <ul class="no-bullet">
    <li><a id="toc-Reverting-broken-commits" href="#Reverting-broken-commits">4.1 Reverting broken commits</a></li>
    <li><a id="toc-Pushing-changes-to-remote-trees" href="#Pushing-changes-to-remote-trees">4.2 Pushing changes to remote trees</a></li>
    <li><a id="toc-Finding-a-specific-svn-revision" href="#Finding-a-specific-svn-revision">4.3 Finding a specific svn revision</a></li>
  </ul></li>
  <li><a id="toc-gpg-key-generation" href="#gpg-key-generation">5 gpg key generation</a></li>
  <li><a id="toc-Pre_002dpush-checklist" href="#Pre_002dpush-checklist">6 Pre-push checklist</a></li>
  <li><a id="toc-Server-Issues" href="#Server-Issues">7 Server Issues</a></li>
</ul>
</div>
</div>

<a name="Introduction"></a>
<h2 class="chapter">1 Introduction<span class="pull-right"><a class="anchor hidden-xs" href="#Introduction" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Introduction" aria-hidden="true">TOC</a></span></h2>

<p>This document aims in giving some quick references on a set of useful Git
commands. You should always use the extensive and detailed documentation
provided directly by Git:
</p>
<div class="example">
<pre class="example">git --help
man git
</pre></div>

<p>shows you the available subcommands,
</p>
<div class="example">
<pre class="example">git &lt;command&gt; --help
man git-&lt;command&gt;
</pre></div>

<p>shows information about the subcommand &lt;command&gt;.
</p>
<p>Additional information could be found on the
<a href="http://gitref.org">Git Reference</a> website.
</p>
<p>For more information about the Git project, visit the
<a href="http://git-scm.com/">Git website</a>.
</p>
<p>Consult these resources whenever you have problems, they are quite exhaustive.
</p>
<p>What follows now is a basic introduction to Git and some FFmpeg-specific
guidelines to ease the contribution to the project.
</p>
<a name="Basics-Usage"></a>
<h2 class="chapter">2 Basics Usage<span class="pull-right"><a class="anchor hidden-xs" href="#Basics-Usage" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Basics-Usage" aria-hidden="true">TOC</a></span></h2>

<a name="Get-Git"></a>
<h3 class="section">2.1 Get Git<span class="pull-right"><a class="anchor hidden-xs" href="#Get-Git" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Get-Git" aria-hidden="true">TOC</a></span></h3>

<p>You can get Git from <a href="http://git-scm.com/">http://git-scm.com/</a>
Most distribution and operating system provide a package for it.
</p>

<a name="Cloning-the-source-tree"></a>
<h3 class="section">2.2 Cloning the source tree<span class="pull-right"><a class="anchor hidden-xs" href="#Cloning-the-source-tree" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Cloning-the-source-tree" aria-hidden="true">TOC</a></span></h3>

<div class="example">
<pre class="example">git clone https://git.ffmpeg.org/ffmpeg.git &lt;target&gt;
</pre></div>

<p>This will put the FFmpeg sources into the directory <var>&lt;target&gt;</var>.
</p>
<div class="example">
<pre class="example"><NAME_EMAIL>:ffmpeg &lt;target&gt;
</pre></div>

<p>This will put the FFmpeg sources into the directory <var>&lt;target&gt;</var> and let
you push back your changes to the remote repository.
</p>
<div class="example">
<pre class="example"><NAME_EMAIL>:ffmpeg-web &lt;target&gt;
</pre></div>

<p>This will put the source of the FFmpeg website into the directory
<var>&lt;target&gt;</var> and let you push back your changes to the remote repository.
(Note that <var>gil</var> stands for GItoLite and is not a typo of <var>git</var>.)
</p>
<p>If you don&rsquo;t have write-access to the ffmpeg-web repository, you can
create patches after making a read-only ffmpeg-web clone:
</p>
<div class="example">
<pre class="example">git clone git://ffmpeg.org/ffmpeg-web &lt;target&gt;
</pre></div>

<p>Make sure that you do not have Windows line endings in your checkouts,
otherwise you may experience spurious compilation failures. One way to
achieve this is to run
</p>
<div class="example">
<pre class="example">git config --global core.autocrlf false
</pre></div>


<span id="Updating-the-source-tree-to-the-latest-revision"></span><a name="Updating-the-source-tree-to-the-latest-revision-1"></a>
<h3 class="section">2.3 Updating the source tree to the latest revision<span class="pull-right"><a class="anchor hidden-xs" href="#Updating-the-source-tree-to-the-latest-revision-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Updating-the-source-tree-to-the-latest-revision-1" aria-hidden="true">TOC</a></span></h3>

<div class="example">
<pre class="example">git pull (--rebase)
</pre></div>

<p>pulls in the latest changes from the tracked branch. The tracked branch
can be remote. By default the master branch tracks the branch master in
the remote origin.
</p>
<div class="warning">
<p><code>--rebase</code> (see below) is recommended.
</p></div>
<a name="Rebasing-your-local-branches"></a>
<h3 class="section">2.4 Rebasing your local branches<span class="pull-right"><a class="anchor hidden-xs" href="#Rebasing-your-local-branches" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Rebasing-your-local-branches" aria-hidden="true">TOC</a></span></h3>

<div class="example">
<pre class="example">git pull --rebase
</pre></div>

<p>fetches the changes from the main repository and replays your local commits
over it. This is required to keep all your local changes at the top of
FFmpeg&rsquo;s master tree. The master tree will reject pushes with merge commits.
</p>

<a name="Adding_002fremoving-files_002fdirectories"></a>
<h3 class="section">2.5 Adding/removing files/directories<span class="pull-right"><a class="anchor hidden-xs" href="#Adding_002fremoving-files_002fdirectories" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Adding_002fremoving-files_002fdirectories" aria-hidden="true">TOC</a></span></h3>

<div class="example">
<pre class="example">git add [-A] &lt;filename/dirname&gt;
git rm [-r] &lt;filename/dirname&gt;
</pre></div>

<p>Git needs to get notified of all changes you make to your working
directory that makes files appear or disappear.
Line moves across files are automatically tracked.
</p>

<a name="Showing-modifications"></a>
<h3 class="section">2.6 Showing modifications<span class="pull-right"><a class="anchor hidden-xs" href="#Showing-modifications" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Showing-modifications" aria-hidden="true">TOC</a></span></h3>

<div class="example">
<pre class="example">git diff &lt;filename(s)&gt;
</pre></div>

<p>will show all local modifications in your working directory as unified diff.
</p>

<a name="Inspecting-the-changelog"></a>
<h3 class="section">2.7 Inspecting the changelog<span class="pull-right"><a class="anchor hidden-xs" href="#Inspecting-the-changelog" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Inspecting-the-changelog" aria-hidden="true">TOC</a></span></h3>

<div class="example">
<pre class="example">git log &lt;filename(s)&gt;
</pre></div>

<p>You may also use the graphical tools like <code>gitview</code> or <code>gitk</code>
or the web interface available at <a href="http://source.ffmpeg.org/">http://source.ffmpeg.org/</a>.
</p>
<a name="Checking-source-tree-status"></a>
<h3 class="section">2.8 Checking source tree status<span class="pull-right"><a class="anchor hidden-xs" href="#Checking-source-tree-status" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Checking-source-tree-status" aria-hidden="true">TOC</a></span></h3>

<div class="example">
<pre class="example">git status
</pre></div>

<p>detects all the changes you made and lists what actions will be taken in case
of a commit (additions, modifications, deletions, etc.).
</p>

<a name="Committing"></a>
<h3 class="section">2.9 Committing<span class="pull-right"><a class="anchor hidden-xs" href="#Committing" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Committing" aria-hidden="true">TOC</a></span></h3>

<div class="example">
<pre class="example">git diff --check
</pre></div>

<p>to double check your changes before committing them to avoid trouble later
on. All experienced developers do this on each and every commit, no matter
how small.
</p>
<p>Every one of them has been saved from looking like a fool by this many times.
It&rsquo;s very easy for stray debug output or cosmetic modifications to slip in,
please avoid problems through this extra level of scrutiny.
</p>
<p>For cosmetics-only commits you should get (almost) empty output from
</p>
<div class="example">
<pre class="example">git diff -w -b &lt;filename(s)&gt;
</pre></div>

<p>Also check the output of
</p>
<div class="example">
<pre class="example">git status
</pre></div>

<p>to make sure you don&rsquo;t have untracked files or deletions.
</p>
<div class="example">
<pre class="example">git add [-i|-p|-A] &lt;filenames/dirnames&gt;
</pre></div>

<p>Make sure you have told Git your name, email address and GPG key
</p>
<div class="example">
<pre class="example">git config --global user.name &quot;My Name&quot;
git config --global user.email <EMAIL>
git config --global user.signingkey ABCDEF0123245
</pre></div>

<p>Enable signing all commits or use -S
</p>
<div class="example">
<pre class="example">git config --global commit.gpgsign true
</pre></div>

<p>Use <samp>--global</samp> to set the global configuration for all your Git checkouts.
</p>
<p>Git will select the changes to the files for commit. Optionally you can use
the interactive or the patch mode to select hunk by hunk what should be
added to the commit.
</p>

<div class="example">
<pre class="example">git commit
</pre></div>

<p>Git will commit the selected changes to your current local branch.
</p>
<p>You will be prompted for a log message in an editor, which is either
set in your personal configuration file through
</p>
<div class="example">
<pre class="example">git config --global core.editor
</pre></div>

<p>or set by one of the following environment variables:
<var>GIT_EDITOR</var>, <var>VISUAL</var> or <var>EDITOR</var>.
</p>
<a name="Writing-a-commit-message"></a>
<h3 class="section">2.10 Writing a commit message<span class="pull-right"><a class="anchor hidden-xs" href="#Writing-a-commit-message" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Writing-a-commit-message" aria-hidden="true">TOC</a></span></h3>

<p>Log messages should be concise but descriptive.
</p>
<p>The first line must contain the context, a colon and a very short
summary of what the commit does. Details can be added, if necessary,
separated by an empty line. These details should not exceed 60-72 characters
per line, except when containing code.
</p>
<p>Example of a good commit message:
</p>
<div class="example">
<pre class="example">avcodec/cbs: add a helper to read extradata within packet side data

Using ff_cbs_read() on the raw buffer will not parse it as extradata,
resulting in parsing errors for example when handling ISOBMFF avcC.
This helper works around that.
</pre></div>

<div class="example">
<pre class="example">ptr might be NULL
</pre></div>

<p>If the summary on the first line is not enough, in the body of the message,
explain why you made a change, what you did will be obvious from the changes
themselves most of the time. Saying just &quot;bug fix&quot; or &quot;10l&quot; is bad. Remember
that people of varying skill levels look at and educate themselves while
reading through your code. Don&rsquo;t include filenames in log messages except in
the context, Git provides that information.
</p>
<p>If the commit fixes a registered issue, state it in a separate line of the
body: <code>Fix Trac ticket #42.</code>
</p>
<p>The first line will be used to name
the patch by <code>git format-patch</code>.
</p>
<p>Common mistakes for the first line, as seen in <code>git log --oneline</code>
include: missing context at the beginning; description of what the code did
before the patch; line too long or wrapped to the second line.
</p>
<a name="Preparing-a-patchset"></a>
<h3 class="section">2.11 Preparing a patchset<span class="pull-right"><a class="anchor hidden-xs" href="#Preparing-a-patchset" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Preparing-a-patchset" aria-hidden="true">TOC</a></span></h3>

<div class="example">
<pre class="example">git format-patch &lt;commit&gt; [-o directory]
</pre></div>

<p>will generate a set of patches for each commit between <var>&lt;commit&gt;</var> and
current <var>HEAD</var>. E.g.
</p>
<div class="example">
<pre class="example">git format-patch origin/master
</pre></div>

<p>will generate patches for all commits on current branch which are not
present in upstream.
A useful shortcut is also
</p>
<div class="example">
<pre class="example">git format-patch -n
</pre></div>

<p>which will generate patches from last <var>n</var> commits.
By default the patches are created in the current directory.
</p>
<a name="Sending-patches-for-review"></a>
<h3 class="section">2.12 Sending patches for review<span class="pull-right"><a class="anchor hidden-xs" href="#Sending-patches-for-review" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Sending-patches-for-review" aria-hidden="true">TOC</a></span></h3>

<div class="example">
<pre class="example">git send-email &lt;commit list|directory&gt;
</pre></div>

<p>will send the patches created by <code>git format-patch</code> or directly
generates them. All the email fields can be configured in the global/local
configuration or overridden by command line.
Note that this tool must often be installed separately (e.g. <var>git-email</var>
package on Debian-based distros).
</p>

<a name="Renaming_002fmoving_002fcopying-files-or-contents-of-files"></a>
<h3 class="section">2.13 Renaming/moving/copying files or contents of files<span class="pull-right"><a class="anchor hidden-xs" href="#Renaming_002fmoving_002fcopying-files-or-contents-of-files" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Renaming_002fmoving_002fcopying-files-or-contents-of-files" aria-hidden="true">TOC</a></span></h3>

<p>Git automatically tracks such changes, making those normal commits.
</p>
<div class="example">
<pre class="example">mv/cp path/file otherpath/otherfile
git add [-A] .
git commit
</pre></div>


<a name="Git-configuration"></a>
<h2 class="chapter">3 Git configuration<span class="pull-right"><a class="anchor hidden-xs" href="#Git-configuration" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Git-configuration" aria-hidden="true">TOC</a></span></h2>

<p>In order to simplify a few workflows, it is advisable to configure both
your personal Git installation and your local FFmpeg repository.
</p>
<a name="Personal-Git-installation"></a>
<h3 class="section">3.1 Personal Git installation<span class="pull-right"><a class="anchor hidden-xs" href="#Personal-Git-installation" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Personal-Git-installation" aria-hidden="true">TOC</a></span></h3>

<p>Add the following to your <samp>~/.gitconfig</samp> to help <code>git send-email</code>
and <code>git format-patch</code> detect renames:
</p>
<div class="example">
<pre class="example">[diff]
        renames = copy
</pre></div>

<a name="Repository-configuration"></a>
<h3 class="section">3.2 Repository configuration<span class="pull-right"><a class="anchor hidden-xs" href="#Repository-configuration" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Repository-configuration" aria-hidden="true">TOC</a></span></h3>

<p>In order to have <code>git send-email</code> automatically send patches
to the ffmpeg-devel mailing list, add the following stanza
to <samp>/path/to/ffmpeg/repository/.git/config</samp>:
</p>
<div class="example">
<pre class="example">[sendemail]
        to = <EMAIL>
</pre></div>

<a name="FFmpeg-specific"></a>
<h2 class="chapter">4 FFmpeg specific<span class="pull-right"><a class="anchor hidden-xs" href="#FFmpeg-specific" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-FFmpeg-specific" aria-hidden="true">TOC</a></span></h2>

<a name="Reverting-broken-commits"></a>
<h3 class="section">4.1 Reverting broken commits<span class="pull-right"><a class="anchor hidden-xs" href="#Reverting-broken-commits" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Reverting-broken-commits" aria-hidden="true">TOC</a></span></h3>

<div class="example">
<pre class="example">git reset &lt;commit&gt;
</pre></div>

<p><code>git reset</code> will uncommit the changes till <var>&lt;commit&gt;</var> rewriting
the current branch history.
</p>
<div class="example">
<pre class="example">git commit --amend
</pre></div>

<p>allows one to amend the last commit details quickly.
</p>
<div class="example">
<pre class="example">git rebase -i origin/master
</pre></div>

<p>will replay local commits over the main repository allowing to edit, merge
or remove some of them in the process.
</p>
<div class="info">
<p><code>git reset</code>, <code>git commit --amend</code> and <code>git rebase</code>
rewrite history, so you should use them ONLY on your local or topic branches.
The main repository will reject those changes.
</p></div>
<div class="example">
<pre class="example">git revert &lt;commit&gt;
</pre></div>

<p><code>git revert</code> will generate a revert commit. This will not make the
faulty commit disappear from the history.
</p>
<a name="Pushing-changes-to-remote-trees"></a>
<h3 class="section">4.2 Pushing changes to remote trees<span class="pull-right"><a class="anchor hidden-xs" href="#Pushing-changes-to-remote-trees" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Pushing-changes-to-remote-trees" aria-hidden="true">TOC</a></span></h3>

<div class="example">
<pre class="example">git push origin master --dry-run
</pre></div>

<p>Will simulate a push of the local master branch to the default remote
(<var>origin</var>). And list which branches and ranges or commits would have been
pushed.
Git will prevent you from pushing changes if the local and remote trees are
out of sync. Refer to <a href="#Updating-the-source-tree-to-the-latest-revision">Updating the source tree to the latest revision</a>.
</p>
<div class="example">
<pre class="example">git remote add &lt;name&gt; &lt;url&gt;
</pre></div>

<p>Will add additional remote with a name reference, it is useful if you want
to push your local branch for review on a remote host.
</p>
<div class="example">
<pre class="example">git push &lt;remote&gt; &lt;refspec&gt;
</pre></div>

<p>Will push the changes to the <var>&lt;remote&gt;</var> repository.
Omitting <var>&lt;refspec&gt;</var> makes <code>git push</code> update all the remote
branches matching the local ones.
</p>
<a name="Finding-a-specific-svn-revision"></a>
<h3 class="section">4.3 Finding a specific svn revision<span class="pull-right"><a class="anchor hidden-xs" href="#Finding-a-specific-svn-revision" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Finding-a-specific-svn-revision" aria-hidden="true">TOC</a></span></h3>

<p>Since version 1.7.1 Git supports &lsquo;<samp>:/foo</samp>&rsquo; syntax for specifying commits
based on a regular expression. see man gitrevisions
</p>
<div class="example">
<pre class="example">git show :/'as revision 23456'
</pre></div>

<p>will show the svn changeset &lsquo;<samp>r23456</samp>&rsquo;. With older Git versions searching in
the <code>git log</code> output is the easiest option (especially if a pager with
search capabilities is used).
</p>
<p>This commit can be checked out with
</p>
<div class="example">
<pre class="example">git checkout -b svn_23456 :/'as revision 23456'
</pre></div>

<p>or for Git &lt; 1.7.1 with
</p>
<div class="example">
<pre class="example">git checkout -b svn_23456 $SHA1
</pre></div>

<p>where <var>$SHA1</var> is the commit hash from the <code>git log</code> output.
</p>

<a name="gpg-key-generation"></a>
<h2 class="chapter">5 gpg key generation<span class="pull-right"><a class="anchor hidden-xs" href="#gpg-key-generation" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-gpg-key-generation" aria-hidden="true">TOC</a></span></h2>

<p>If you have no gpg key yet, we recommend that you create a ed25519 based key as it
is small, fast and secure. Especially it results in small signatures in git.
</p>
<div class="example">
<pre class="example">gpg --default-new-key-algo &quot;ed25519/cert,sign+cv25519/encr&quot; --quick-generate-key &quot;<EMAIL>&quot;
</pre></div>

<p>When generating a key, make sure the email specified matches the email used in git as some sites like
github consider mismatches a reason to declare such commits unverified. After generating a key you
can add it to the MAINTAINER file and upload it to a keyserver.
</p>
<a name="Pre_002dpush-checklist"></a>
<h2 class="chapter">6 Pre-push checklist<span class="pull-right"><a class="anchor hidden-xs" href="#Pre_002dpush-checklist" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Pre_002dpush-checklist" aria-hidden="true">TOC</a></span></h2>

<p>Once you have a set of commits that you feel are ready for pushing,
work through the following checklist to doublecheck everything is in
proper order. This list tries to be exhaustive. In case you are just
pushing a typo in a comment, some of the steps may be unnecessary.
Apply your common sense, but if in doubt, err on the side of caution.
</p>
<p>First, make sure that the commits and branches you are going to push
match what you want pushed and that nothing is missing, extraneous or
wrong. You can see what will be pushed by running the git push command
with <samp>--dry-run</samp> first. And then inspecting the commits listed with
<code>git log -p 1234567..987654</code>. The <code>git status</code> command
may help in finding local changes that have been forgotten to be added.
</p>
<p>Next let the code pass through a full run of our test suite.
</p>
<ul>
<li> <code>make distclean</code>
</li><li> <code>/path/to/ffmpeg/configure</code>
</li><li> <code>make fate</code>
</li><li> if fate fails due to missing samples run <code>make fate-rsync</code> and retry
</li></ul>

<p>Make sure all your changes have been checked before pushing them, the
test suite only checks against regressions and that only to some extend. It does
obviously not check newly added features/code to be working unless you have
added a test for that (which is recommended).
</p>
<p>Also note that every single commit should pass the test suite, not just
the result of a series of patches.
</p>
<p>Once everything passed, push the changes to your public ffmpeg clone and post a
merge request to ffmpeg-devel. You can also push them directly but this is not
recommended.
</p>
<a name="Server-Issues"></a>
<h2 class="chapter">7 Server Issues<span class="pull-right"><a class="anchor hidden-xs" href="#Server-Issues" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Server-Issues" aria-hidden="true">TOC</a></span></h2>

<p>Contact the project admins at <a href="mailto:<EMAIL>"><EMAIL></a> if you have technical
problems with the Git server.
</p>      <p style="font-size: small;">
        This document was generated using <a href="https://www.gnu.org/software/texinfo/"><em>makeinfo</em></a>.
      </p>
    </div>
  </body>
</html>
