<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Created by GNU Texinfo 6.8, https://www.gnu.org/software/texinfo/ -->
  <head>
    <meta charset="utf-8">
    <title>
      FFmpeg Scaler Documentation
    </title>
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="stylesheet" type="text/css" href="bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="style.min.css">
  </head>
  <body>
    <div class="container">
      <h1>
      FFmpeg Scaler Documentation
      </h1>
<div align="center">
</div>


<a name="SEC_Top"></a>

<div class="Contents_element" id="SEC_Contents">
<h2 class="contents-heading">Table of Contents</h2>

<div class="contents">

<ul class="no-bullet">
  <li><a id="toc-Description" href="#Description">1 Description</a></li>
  <li><a id="toc-Scaler-Options" href="#Scaler-Options">2 Scaler Options</a></li>
  <li><a id="toc-See-Also" href="#See-Also">3 See Also</a></li>
  <li><a id="toc-Authors" href="#Authors">4 Authors</a></li>
</ul>
</div>
</div>

<a name="Description"></a>
<h2 class="chapter">1 Description<span class="pull-right"><a class="anchor hidden-xs" href="#Description" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Description" aria-hidden="true">TOC</a></span></h2>

<p>The FFmpeg rescaler provides a high-level interface to the libswscale
library image conversion utilities. In particular it allows one to perform
image rescaling and pixel format conversion.
</p>

<span id="scaler_005foptions"></span><a name="Scaler-Options"></a>
<h2 class="chapter">2 Scaler Options<span class="pull-right"><a class="anchor hidden-xs" href="#Scaler-Options" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Scaler-Options" aria-hidden="true">TOC</a></span></h2>

<p>The video scaler supports the following named options.
</p>
<p>Options may be set by specifying -<var>option</var> <var>value</var> in the
FFmpeg tools, with a few API-only exceptions noted below.
For programmatic use, they can be set explicitly in the
<code>SwsContext</code> options or through the <samp>libavutil/opt.h</samp> API.
</p>
<dl compact="compact">
<dd>
<span id="sws_005fflags"></span></dd>
<dt><span><samp>sws_flags</samp></span></dt>
<dd><p>Set the scaler flags. This is also used to set the scaling
algorithm. Only a single algorithm should be selected. Default
value is &lsquo;<samp>bicubic</samp>&rsquo;.
</p>
<p>It accepts the following values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>fast_bilinear</samp>&rsquo;</span></dt>
<dd><p>Select fast bilinear scaling algorithm.
</p>
</dd>
<dt><span>&lsquo;<samp>bilinear</samp>&rsquo;</span></dt>
<dd><p>Select bilinear scaling algorithm.
</p>
</dd>
<dt><span>&lsquo;<samp>bicubic</samp>&rsquo;</span></dt>
<dd><p>Select bicubic scaling algorithm.
</p>
</dd>
<dt><span>&lsquo;<samp>experimental</samp>&rsquo;</span></dt>
<dd><p>Select experimental scaling algorithm.
</p>
</dd>
<dt><span>&lsquo;<samp>neighbor</samp>&rsquo;</span></dt>
<dd><p>Select nearest neighbor rescaling algorithm.
</p>
</dd>
<dt><span>&lsquo;<samp>area</samp>&rsquo;</span></dt>
<dd><p>Select averaging area rescaling algorithm.
</p>
</dd>
<dt><span>&lsquo;<samp>bicublin</samp>&rsquo;</span></dt>
<dd><p>Select bicubic scaling algorithm for the luma component, bilinear for
chroma components.
</p>
</dd>
<dt><span>&lsquo;<samp>gauss</samp>&rsquo;</span></dt>
<dd><p>Select Gaussian rescaling algorithm.
</p>
</dd>
<dt><span>&lsquo;<samp>sinc</samp>&rsquo;</span></dt>
<dd><p>Select sinc rescaling algorithm.
</p>
</dd>
<dt><span>&lsquo;<samp>lanczos</samp>&rsquo;</span></dt>
<dd><p>Select Lanczos rescaling algorithm. The default width (alpha) is 3 and can be
changed by setting <code>param0</code>.
</p>
</dd>
<dt><span>&lsquo;<samp>spline</samp>&rsquo;</span></dt>
<dd><p>Select natural bicubic spline rescaling algorithm.
</p>
</dd>
<dt><span>&lsquo;<samp>print_info</samp>&rsquo;</span></dt>
<dd><p>Enable printing/debug logging.
</p>
</dd>
<dt><span>&lsquo;<samp>accurate_rnd</samp>&rsquo;</span></dt>
<dd><p>Enable accurate rounding.
</p>
</dd>
<dt><span>&lsquo;<samp>full_chroma_int</samp>&rsquo;</span></dt>
<dd><p>Enable full chroma interpolation.
</p>
</dd>
<dt><span>&lsquo;<samp>full_chroma_inp</samp>&rsquo;</span></dt>
<dd><p>Select full chroma input.
</p>
</dd>
<dt><span>&lsquo;<samp>bitexact</samp>&rsquo;</span></dt>
<dd><p>Enable bitexact output.
</p></dd>
</dl>

</dd>
<dt><span><samp>srcw <var>(API only)</var></samp></span></dt>
<dd><p>Set source width.
</p>
</dd>
<dt><span><samp>srch <var>(API only)</var></samp></span></dt>
<dd><p>Set source height.
</p>
</dd>
<dt><span><samp>dstw <var>(API only)</var></samp></span></dt>
<dd><p>Set destination width.
</p>
</dd>
<dt><span><samp>dsth <var>(API only)</var></samp></span></dt>
<dd><p>Set destination height.
</p>
</dd>
<dt><span><samp>src_format <var>(API only)</var></samp></span></dt>
<dd><p>Set source pixel format (must be expressed as an integer).
</p>
</dd>
<dt><span><samp>dst_format <var>(API only)</var></samp></span></dt>
<dd><p>Set destination pixel format (must be expressed as an integer).
</p>
</dd>
<dt><span><samp>src_range <var>(boolean)</var></samp></span></dt>
<dd><p>If value is set to <code>1</code>, indicates source is full range. Default value is
<code>0</code>, which indicates source is limited range.
</p>
</dd>
<dt><span><samp>dst_range <var>(boolean)</var></samp></span></dt>
<dd><p>If value is set to <code>1</code>, enable full range for destination. Default value
is <code>0</code>, which enables limited range.
</p>
<span id="sws_005fparams"></span></dd>
<dt><span><samp>param0, param1</samp></span></dt>
<dd><p>Set scaling algorithm parameters. The specified values are specific of
some scaling algorithms and ignored by others. The specified values
are floating point number values.
</p>
</dd>
<dt><span><samp>sws_dither</samp></span></dt>
<dd><p>Set the dithering algorithm. Accepts one of the following
values. Default value is &lsquo;<samp>auto</samp>&rsquo;.
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>auto</samp>&rsquo;</span></dt>
<dd><p>automatic choice
</p>
</dd>
<dt><span>&lsquo;<samp>none</samp>&rsquo;</span></dt>
<dd><p>no dithering
</p>
</dd>
<dt><span>&lsquo;<samp>bayer</samp>&rsquo;</span></dt>
<dd><p>bayer dither
</p>
</dd>
<dt><span>&lsquo;<samp>ed</samp>&rsquo;</span></dt>
<dd><p>error diffusion dither
</p>
</dd>
<dt><span>&lsquo;<samp>a_dither</samp>&rsquo;</span></dt>
<dd><p>arithmetic dither, based using addition
</p>
</dd>
<dt><span>&lsquo;<samp>x_dither</samp>&rsquo;</span></dt>
<dd><p>arithmetic dither, based using xor (more random/less apparent patterning that
a_dither).
</p>
</dd>
</dl>

</dd>
<dt><span><samp>alphablend</samp></span></dt>
<dd><p>Set the alpha blending to use when the input has alpha but the output does not.
Default value is &lsquo;<samp>none</samp>&rsquo;.
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>uniform_color</samp>&rsquo;</span></dt>
<dd><p>Blend onto a uniform background color
</p>
</dd>
<dt><span>&lsquo;<samp>checkerboard</samp>&rsquo;</span></dt>
<dd><p>Blend onto a checkerboard
</p>
</dd>
<dt><span>&lsquo;<samp>none</samp>&rsquo;</span></dt>
<dd><p>No blending
</p>
</dd>
</dl>

</dd>
</dl>


<a name="See-Also"></a>
<h2 class="chapter">3 See Also<span class="pull-right"><a class="anchor hidden-xs" href="#See-Also" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-See-Also" aria-hidden="true">TOC</a></span></h2>

<p><a href="ffmpeg.html">ffmpeg</a>, <a href="ffplay.html">ffplay</a>, <a href="ffprobe.html">ffprobe</a>,
<a href="libswscale.html">libswscale</a>
</p>

<a name="Authors"></a>
<h2 class="chapter">4 Authors<span class="pull-right"><a class="anchor hidden-xs" href="#Authors" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Authors" aria-hidden="true">TOC</a></span></h2>

<p>The FFmpeg developers.
</p>
<p>For details about the authorship, see the Git history of the project
(https://git.ffmpeg.org/ffmpeg), e.g. by typing the command
<code>git log</code> in the FFmpeg source directory, or browsing the
online repository at <a href="https://git.ffmpeg.org/ffmpeg">https://git.ffmpeg.org/ffmpeg</a>.
</p>
<p>Maintainers for the specific components are listed in the file
<samp>MAINTAINERS</samp> in the source code tree.
</p>

      <p style="font-size: small;">
        This document was generated using <a href="https://www.gnu.org/software/texinfo/"><em>makeinfo</em></a>.
      </p>
    </div>
  </body>
</html>
