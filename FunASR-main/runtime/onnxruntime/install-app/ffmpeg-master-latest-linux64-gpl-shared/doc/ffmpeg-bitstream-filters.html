<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<!-- Created by GNU Texinfo 6.8, https://www.gnu.org/software/texinfo/ -->
  <head>
    <meta charset="utf-8">
    <title>
      FFmpeg Bitstream Filters Documentation
    </title>
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="stylesheet" type="text/css" href="bootstrap.min.css">
    <link rel="stylesheet" type="text/css" href="style.min.css">
  </head>
  <body>
    <div class="container">
      <h1>
      FFmpeg Bitstream Filters Documentation
      </h1>
<div align="center">
</div>


<a name="SEC_Top"></a>

<div class="Contents_element" id="SEC_Contents">
<h2 class="contents-heading">Table of Contents</h2>

<div class="contents">

<ul class="no-bullet">
  <li><a id="toc-Description" href="#Description">1 Description</a></li>
  <li><a id="toc-Bitstream-Filters" href="#Bitstream-Filters">2 Bitstream Filters</a>
  <ul class="no-bullet">
    <li><a id="toc-aac_005fadtstoasc" href="#aac_005fadtstoasc">2.1 aac_adtstoasc</a></li>
    <li><a id="toc-av1_005fmetadata" href="#av1_005fmetadata">2.2 av1_metadata</a></li>
    <li><a id="toc-chomp" href="#chomp">2.3 chomp</a></li>
    <li><a id="toc-dca_005fcore" href="#dca_005fcore">2.4 dca_core</a></li>
    <li><a id="toc-dump_005fextra" href="#dump_005fextra">2.5 dump_extra</a></li>
    <li><a id="toc-dv_005ferror_005fmarker" href="#dv_005ferror_005fmarker">2.6 dv_error_marker</a></li>
    <li><a id="toc-eac3_005fcore" href="#eac3_005fcore">2.7 eac3_core</a></li>
    <li><a id="toc-extract_005fextradata" href="#extract_005fextradata">2.8 extract_extradata</a></li>
    <li><a id="toc-filter_005funits" href="#filter_005funits">2.9 filter_units</a></li>
    <li><a id="toc-hapqa_005fextract" href="#hapqa_005fextract">2.10 hapqa_extract</a></li>
    <li><a id="toc-h264_005fmetadata" href="#h264_005fmetadata">2.11 h264_metadata</a></li>
    <li><a id="toc-h264_005fmp4toannexb" href="#h264_005fmp4toannexb">2.12 h264_mp4toannexb</a></li>
    <li><a id="toc-h264_005fredundant_005fpps" href="#h264_005fredundant_005fpps">2.13 h264_redundant_pps</a></li>
    <li><a id="toc-hevc_005fmetadata" href="#hevc_005fmetadata">2.14 hevc_metadata</a></li>
    <li><a id="toc-hevc_005fmp4toannexb" href="#hevc_005fmp4toannexb">2.15 hevc_mp4toannexb</a></li>
    <li><a id="toc-imxdump" href="#imxdump">2.16 imxdump</a></li>
    <li><a id="toc-mjpeg2jpeg" href="#mjpeg2jpeg">2.17 mjpeg2jpeg</a></li>
    <li><a id="toc-mjpegadump" href="#mjpegadump">2.18 mjpegadump</a></li>
    <li><a id="toc-mov2textsub-1" href="#mov2textsub-1">2.19 mov2textsub</a></li>
    <li><a id="toc-mp3decomp" href="#mp3decomp">2.20 mp3decomp</a></li>
    <li><a id="toc-mpeg2_005fmetadata" href="#mpeg2_005fmetadata">2.21 mpeg2_metadata</a></li>
    <li><a id="toc-mpeg4_005funpack_005fbframes" href="#mpeg4_005funpack_005fbframes">2.22 mpeg4_unpack_bframes</a></li>
    <li><a id="toc-noise" href="#noise">2.23 noise</a>
    <ul class="no-bullet">
      <li><a id="toc-Examples" href="#Examples">2.23.1 Examples</a></li>
    </ul></li>
    <li><a id="toc-null" href="#null">2.24 null</a></li>
    <li><a id="toc-pcm_005frechunk" href="#pcm_005frechunk">2.25 pcm_rechunk</a></li>
    <li><a id="toc-pgs_005fframe_005fmerge" href="#pgs_005fframe_005fmerge">2.26 pgs_frame_merge</a></li>
    <li><a id="toc-prores_005fmetadata" href="#prores_005fmetadata">2.27 prores_metadata</a></li>
    <li><a id="toc-remove_005fextra" href="#remove_005fextra">2.28 remove_extra</a></li>
    <li><a id="toc-setts" href="#setts">2.29 setts</a></li>
    <li><a id="toc-text2movsub-1" href="#text2movsub-1">2.30 text2movsub</a></li>
    <li><a id="toc-trace_005fheaders" href="#trace_005fheaders">2.31 trace_headers</a></li>
    <li><a id="toc-truehd_005fcore" href="#truehd_005fcore">2.32 truehd_core</a></li>
    <li><a id="toc-vp9_005fmetadata" href="#vp9_005fmetadata">2.33 vp9_metadata</a></li>
    <li><a id="toc-vp9_005fsuperframe" href="#vp9_005fsuperframe">2.34 vp9_superframe</a></li>
    <li><a id="toc-vp9_005fsuperframe_005fsplit" href="#vp9_005fsuperframe_005fsplit">2.35 vp9_superframe_split</a></li>
    <li><a id="toc-vp9_005fraw_005freorder" href="#vp9_005fraw_005freorder">2.36 vp9_raw_reorder</a></li>
  </ul></li>
  <li><a id="toc-See-Also" href="#See-Also">3 See Also</a></li>
  <li><a id="toc-Authors" href="#Authors">4 Authors</a></li>
</ul>
</div>
</div>

<a name="Description"></a>
<h2 class="chapter">1 Description<span class="pull-right"><a class="anchor hidden-xs" href="#Description" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Description" aria-hidden="true">TOC</a></span></h2>

<p>This document describes the bitstream filters provided by the
libavcodec library.
</p>
<p>A bitstream filter operates on the encoded stream data, and performs
bitstream level modifications without performing decoding.
</p>

<a name="Bitstream-Filters"></a>
<h2 class="chapter">2 Bitstream Filters<span class="pull-right"><a class="anchor hidden-xs" href="#Bitstream-Filters" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Bitstream-Filters" aria-hidden="true">TOC</a></span></h2>

<p>When you configure your FFmpeg build, all the supported bitstream
filters are enabled by default. You can list all available ones using
the configure option <code>--list-bsfs</code>.
</p>
<p>You can disable all the bitstream filters using the configure option
<code>--disable-bsfs</code>, and selectively enable any bitstream filter using
the option <code>--enable-bsf=BSF</code>, or you can disable a particular
bitstream filter using the option <code>--disable-bsf=BSF</code>.
</p>
<p>The option <code>-bsfs</code> of the ff* tools will display the list of
all the supported bitstream filters included in your build.
</p>
<p>The ff* tools have a -bsf option applied per stream, taking a
comma-separated list of filters, whose parameters follow the filter
name after a &rsquo;=&rsquo;.
</p>
<div class="example">
<pre class="example">ffmpeg -i INPUT -c:v copy -bsf:v filter1[=opt1=str1:opt2=str2][,filter2] OUTPUT
</pre></div>

<p>Below is a description of the currently available bitstream filters,
with their parameters, if any.
</p>
<a name="aac_005fadtstoasc"></a>
<h3 class="section">2.1 aac_adtstoasc<span class="pull-right"><a class="anchor hidden-xs" href="#aac_005fadtstoasc" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-aac_005fadtstoasc" aria-hidden="true">TOC</a></span></h3>

<p>Convert MPEG-2/4 AAC ADTS to an MPEG-4 Audio Specific Configuration
bitstream.
</p>
<p>This filter creates an MPEG-4 AudioSpecificConfig from an MPEG-2/4
ADTS header and removes the ADTS header.
</p>
<p>This filter is required for example when copying an AAC stream from a
raw ADTS AAC or an MPEG-TS container to MP4A-LATM, to an FLV file, or
to MOV/MP4 files and related formats such as 3GP or M4A. Please note
that it is auto-inserted for MP4A-LATM and MOV/MP4 and related formats.
</p>
<a name="av1_005fmetadata"></a>
<h3 class="section">2.2 av1_metadata<span class="pull-right"><a class="anchor hidden-xs" href="#av1_005fmetadata" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-av1_005fmetadata" aria-hidden="true">TOC</a></span></h3>

<p>Modify metadata embedded in an AV1 stream.
</p>
<dl compact="compact">
<dt><span><samp>td</samp></span></dt>
<dd><p>Insert or remove temporal delimiter OBUs in all temporal units of the
stream.
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>insert</samp>&rsquo;</span></dt>
<dd><p>Insert a TD at the beginning of every TU which does not already have one.
</p></dd>
<dt><span>&lsquo;<samp>remove</samp>&rsquo;</span></dt>
<dd><p>Remove the TD from the beginning of every TU which has one.
</p></dd>
</dl>

</dd>
<dt><span><samp>color_primaries</samp></span></dt>
<dt><span><samp>transfer_characteristics</samp></span></dt>
<dt><span><samp>matrix_coefficients</samp></span></dt>
<dd><p>Set the color description fields in the stream (see AV1 section 6.4.2).
</p>
</dd>
<dt><span><samp>color_range</samp></span></dt>
<dd><p>Set the color range in the stream (see AV1 section 6.4.2; note that
this cannot be set for streams using BT.709 primaries, sRGB transfer
characteristic and identity (RGB) matrix coefficients).
</p><dl compact="compact">
<dt><span>&lsquo;<samp>tv</samp>&rsquo;</span></dt>
<dd><p>Limited range.
</p></dd>
<dt><span>&lsquo;<samp>pc</samp>&rsquo;</span></dt>
<dd><p>Full range.
</p></dd>
</dl>

</dd>
<dt><span><samp>chroma_sample_position</samp></span></dt>
<dd><p>Set the chroma sample location in the stream (see AV1 section 6.4.2).
This can only be set for 4:2:0 streams.
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>vertical</samp>&rsquo;</span></dt>
<dd><p>Left position (matching the default in MPEG-2 and H.264).
</p></dd>
<dt><span>&lsquo;<samp>colocated</samp>&rsquo;</span></dt>
<dd><p>Top-left position.
</p></dd>
</dl>

</dd>
<dt><span><samp>tick_rate</samp></span></dt>
<dd><p>Set the tick rate (<em>time_scale / num_units_in_display_tick</em>) in
the timing info in the sequence header.
</p></dd>
<dt><span><samp>num_ticks_per_picture</samp></span></dt>
<dd><p>Set the number of ticks in each picture, to indicate that the stream
has a fixed framerate.  Ignored if <samp>tick_rate</samp> is not also set.
</p>
</dd>
<dt><span><samp>delete_padding</samp></span></dt>
<dd><p>Deletes Padding OBUs.
</p>
</dd>
</dl>

<a name="chomp"></a>
<h3 class="section">2.3 chomp<span class="pull-right"><a class="anchor hidden-xs" href="#chomp" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-chomp" aria-hidden="true">TOC</a></span></h3>

<p>Remove zero padding at the end of a packet.
</p>
<a name="dca_005fcore"></a>
<h3 class="section">2.4 dca_core<span class="pull-right"><a class="anchor hidden-xs" href="#dca_005fcore" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-dca_005fcore" aria-hidden="true">TOC</a></span></h3>

<p>Extract the core from a DCA/DTS stream, dropping extensions such as
DTS-HD.
</p>
<a name="dump_005fextra"></a>
<h3 class="section">2.5 dump_extra<span class="pull-right"><a class="anchor hidden-xs" href="#dump_005fextra" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-dump_005fextra" aria-hidden="true">TOC</a></span></h3>

<p>Add extradata to the beginning of the filtered packets except when
said packets already exactly begin with the extradata that is intended
to be added.
</p>
<dl compact="compact">
<dt><span><samp>freq</samp></span></dt>
<dd><p>The additional argument specifies which packets should be filtered.
It accepts the values:
</p><dl compact="compact">
<dt><span>&lsquo;<samp>k</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>keyframe</samp>&rsquo;</span></dt>
<dd><p>add extradata to all key packets
</p>
</dd>
<dt><span>&lsquo;<samp>e</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>all</samp>&rsquo;</span></dt>
<dd><p>add extradata to all packets
</p></dd>
</dl>
</dd>
</dl>

<p>If not specified it is assumed &lsquo;<samp>k</samp>&rsquo;.
</p>
<p>For example the following <code>ffmpeg</code> command forces a global
header (thus disabling individual packet headers) in the H.264 packets
generated by the <code>libx264</code> encoder, but corrects them by adding
the header stored in extradata to the key packets:
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -map 0 -flags:v +global_header -c:v libx264 -bsf:v dump_extra out.ts
</pre></div>

<a name="dv_005ferror_005fmarker"></a>
<h3 class="section">2.6 dv_error_marker<span class="pull-right"><a class="anchor hidden-xs" href="#dv_005ferror_005fmarker" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-dv_005ferror_005fmarker" aria-hidden="true">TOC</a></span></h3>

<p>Blocks in DV which are marked as damaged are replaced by blocks of the specified color.
</p>
<dl compact="compact">
<dt><span><samp>color</samp></span></dt>
<dd><p>The color to replace damaged blocks by
</p></dd>
<dt><span><samp>sta</samp></span></dt>
<dd><p>A 16 bit mask which specifies which of the 16 possible error status values are
to be replaced by colored blocks. 0xFFFE is the default which replaces all non 0
error status values.
</p><dl compact="compact">
<dt><span>&lsquo;<samp>ok</samp>&rsquo;</span></dt>
<dd><p>No error, no concealment
</p></dd>
<dt><span>&lsquo;<samp>err</samp>&rsquo;</span></dt>
<dd><p>Error, No concealment
</p></dd>
<dt><span>&lsquo;<samp>res</samp>&rsquo;</span></dt>
<dd><p>Reserved
</p></dd>
<dt><span>&lsquo;<samp>notok</samp>&rsquo;</span></dt>
<dd><p>Error or concealment
</p></dd>
<dt><span>&lsquo;<samp>notres</samp>&rsquo;</span></dt>
<dd><p>Not reserved
</p></dd>
<dt><span>&lsquo;<samp>Aa, Ba, Ca, Ab, Bb, Cb, A, B, C, a, b, erri, erru</samp>&rsquo;</span></dt>
<dd><p>The specific error status code
</p></dd>
</dl>
<p>see page 44-46 or section 5.5 of
<a href="http://web.archive.org/web/20060927044735/http://www.smpte.org/smpte_store/standards/pdf/s314m.pdf">http://web.archive.org/web/20060927044735/http://www.smpte.org/smpte_store/standards/pdf/s314m.pdf</a>
</p>
</dd>
</dl>

<a name="eac3_005fcore"></a>
<h3 class="section">2.7 eac3_core<span class="pull-right"><a class="anchor hidden-xs" href="#eac3_005fcore" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-eac3_005fcore" aria-hidden="true">TOC</a></span></h3>

<p>Extract the core from a E-AC-3 stream, dropping extra channels.
</p>
<a name="extract_005fextradata"></a>
<h3 class="section">2.8 extract_extradata<span class="pull-right"><a class="anchor hidden-xs" href="#extract_005fextradata" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-extract_005fextradata" aria-hidden="true">TOC</a></span></h3>

<p>Extract the in-band extradata.
</p>
<p>Certain codecs allow the long-term headers (e.g. MPEG-2 sequence headers,
or H.264/HEVC (VPS/)SPS/PPS) to be transmitted either &quot;in-band&quot; (i.e. as a part
of the bitstream containing the coded frames) or &quot;out of band&quot; (e.g. on the
container level). This latter form is called &quot;extradata&quot; in FFmpeg terminology.
</p>
<p>This bitstream filter detects the in-band headers and makes them available as
extradata.
</p>
<dl compact="compact">
<dt><span><samp>remove</samp></span></dt>
<dd><p>When this option is enabled, the long-term headers are removed from the
bitstream after extraction.
</p></dd>
</dl>

<a name="filter_005funits"></a>
<h3 class="section">2.9 filter_units<span class="pull-right"><a class="anchor hidden-xs" href="#filter_005funits" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-filter_005funits" aria-hidden="true">TOC</a></span></h3>

<p>Remove units with types in or not in a given set from the stream.
</p>
<dl compact="compact">
<dt><span><samp>pass_types</samp></span></dt>
<dd><p>List of unit types or ranges of unit types to pass through while removing
all others.  This is specified as a &rsquo;|&rsquo;-separated list of unit type values
or ranges of values with &rsquo;-&rsquo;.
</p>
</dd>
<dt><span><samp>remove_types</samp></span></dt>
<dd><p>Identical to <samp>pass_types</samp>, except the units in the given set
removed and all others passed through.
</p></dd>
</dl>

<p>Extradata is unchanged by this transformation, but note that if the stream
contains inline parameter sets then the output may be unusable if they are
removed.
</p>
<p>For example, to remove all non-VCL NAL units from an H.264 stream:
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -c:v copy -bsf:v 'filter_units=pass_types=1-5' OUTPUT
</pre></div>

<p>To remove all AUDs, SEI and filler from an H.265 stream:
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -c:v copy -bsf:v 'filter_units=remove_types=35|38-40' OUTPUT
</pre></div>

<a name="hapqa_005fextract"></a>
<h3 class="section">2.10 hapqa_extract<span class="pull-right"><a class="anchor hidden-xs" href="#hapqa_005fextract" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-hapqa_005fextract" aria-hidden="true">TOC</a></span></h3>

<p>Extract Rgb or Alpha part of an HAPQA file, without recompression, in order to create an HAPQ or an HAPAlphaOnly file.
</p>
<dl compact="compact">
<dt><span><samp>texture</samp></span></dt>
<dd><p>Specifies the texture to keep.
</p>
<dl compact="compact">
<dt><span><samp>color</samp></span></dt>
<dt><span><samp>alpha</samp></span></dt>
</dl>

</dd>
</dl>

<p>Convert HAPQA to HAPQ
</p><div class="example">
<pre class="example">ffmpeg -i hapqa_inputfile.mov -c copy -bsf:v hapqa_extract=texture=color -tag:v HapY -metadata:s:v:0 encoder=&quot;HAPQ&quot; hapq_file.mov
</pre></div>

<p>Convert HAPQA to HAPAlphaOnly
</p><div class="example">
<pre class="example">ffmpeg -i hapqa_inputfile.mov -c copy -bsf:v hapqa_extract=texture=alpha -tag:v HapA -metadata:s:v:0 encoder=&quot;HAPAlpha Only&quot; hapalphaonly_file.mov
</pre></div>

<a name="h264_005fmetadata"></a>
<h3 class="section">2.11 h264_metadata<span class="pull-right"><a class="anchor hidden-xs" href="#h264_005fmetadata" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-h264_005fmetadata" aria-hidden="true">TOC</a></span></h3>

<p>Modify metadata embedded in an H.264 stream.
</p>
<dl compact="compact">
<dt><span><samp>aud</samp></span></dt>
<dd><p>Insert or remove AUD NAL units in all access units of the stream.
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>pass</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>insert</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>remove</samp>&rsquo;</span></dt>
</dl>

<p>Default is pass.
</p>
</dd>
<dt><span><samp>sample_aspect_ratio</samp></span></dt>
<dd><p>Set the sample aspect ratio of the stream in the VUI parameters.
See H.264 table E-1.
</p>
</dd>
<dt><span><samp>overscan_appropriate_flag</samp></span></dt>
<dd><p>Set whether the stream is suitable for display using overscan
or not (see H.264 section E.2.1).
</p>
</dd>
<dt><span><samp>video_format</samp></span></dt>
<dt><span><samp>video_full_range_flag</samp></span></dt>
<dd><p>Set the video format in the stream (see H.264 section E.2.1 and
table E-2).
</p>
</dd>
<dt><span><samp>colour_primaries</samp></span></dt>
<dt><span><samp>transfer_characteristics</samp></span></dt>
<dt><span><samp>matrix_coefficients</samp></span></dt>
<dd><p>Set the colour description in the stream (see H.264 section E.2.1
and tables E-3, E-4 and E-5).
</p>
</dd>
<dt><span><samp>chroma_sample_loc_type</samp></span></dt>
<dd><p>Set the chroma sample location in the stream (see H.264 section
E.2.1 and figure E-1).
</p>
</dd>
<dt><span><samp>tick_rate</samp></span></dt>
<dd><p>Set the tick rate (time_scale / num_units_in_tick) in the VUI
parameters.  This is the smallest time unit representable in the
stream, and in many cases represents the field rate of the stream
(double the frame rate).
</p></dd>
<dt><span><samp>fixed_frame_rate_flag</samp></span></dt>
<dd><p>Set whether the stream has fixed framerate - typically this indicates
that the framerate is exactly half the tick rate, but the exact
meaning is dependent on interlacing and the picture structure (see
H.264 section E.2.1 and table E-6).
</p></dd>
<dt><span><samp>zero_new_constraint_set_flags</samp></span></dt>
<dd><p>Zero constraint_set4_flag and constraint_set5_flag in the SPS. These
bits were reserved in a previous version of the H.264 spec, and thus
some hardware decoders require these to be zero. The result of zeroing
this is still a valid bitstream.
</p>
</dd>
<dt><span><samp>crop_left</samp></span></dt>
<dt><span><samp>crop_right</samp></span></dt>
<dt><span><samp>crop_top</samp></span></dt>
<dt><span><samp>crop_bottom</samp></span></dt>
<dd><p>Set the frame cropping offsets in the SPS.  These values will replace
the current ones if the stream is already cropped.
</p>
<p>These fields are set in pixels.  Note that some sizes may not be
representable if the chroma is subsampled or the stream is interlaced
(see H.264 section 7.4.2.1.1).
</p>
</dd>
<dt><span><samp>sei_user_data</samp></span></dt>
<dd><p>Insert a string as SEI unregistered user data.  The argument must
be of the form <em>UUID+string</em>, where the UUID is as hex digits
possibly separated by hyphens, and the string can be anything.
</p>
<p>For example, &lsquo;<samp>086f3693-b7b3-4f2c-9653-21492feee5b8+hello</samp>&rsquo; will
insert the string &ldquo;hello&rdquo; associated with the given UUID.
</p>
</dd>
<dt><span><samp>delete_filler</samp></span></dt>
<dd><p>Deletes both filler NAL units and filler SEI messages.
</p>
</dd>
<dt><span><samp>display_orientation</samp></span></dt>
<dd><p>Insert, extract or remove Display orientation SEI messages.
See H.264 section D.1.27 and D.2.27 for syntax and semantics.
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>pass</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>insert</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>remove</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>extract</samp>&rsquo;</span></dt>
</dl>

<p>Default is pass.
</p>
<p>Insert mode works in conjunction with <code>rotate</code> and <code>flip</code> options.
Any pre-existing Display orientation messages will be removed in insert or remove mode.
Extract mode attaches the display matrix to the packet as side data.
</p>
</dd>
<dt><span><samp>rotate</samp></span></dt>
<dd><p>Set rotation in display orientation SEI (anticlockwise angle in degrees).
Range is -360 to +360. Default is NaN.
</p>
</dd>
<dt><span><samp>flip</samp></span></dt>
<dd><p>Set flip in display orientation SEI.
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>horizontal</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>vertical</samp>&rsquo;</span></dt>
</dl>

<p>Default is unset.
</p>
</dd>
<dt><span><samp>level</samp></span></dt>
<dd><p>Set the level in the SPS.  Refer to H.264 section A.3 and tables A-1
to A-5.
</p>
<p>The argument must be the name of a level (for example, &lsquo;<samp>4.2</samp>&rsquo;), a
level_idc value (for example, &lsquo;<samp>42</samp>&rsquo;), or the special name &lsquo;<samp>auto</samp>&rsquo;
indicating that the filter should attempt to guess the level from the
input stream properties.
</p>
</dd>
</dl>

<a name="h264_005fmp4toannexb"></a>
<h3 class="section">2.12 h264_mp4toannexb<span class="pull-right"><a class="anchor hidden-xs" href="#h264_005fmp4toannexb" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-h264_005fmp4toannexb" aria-hidden="true">TOC</a></span></h3>

<p>Convert an H.264 bitstream from length prefixed mode to start code
prefixed mode (as defined in the Annex B of the ITU-T H.264
specification).
</p>
<p>This is required by some streaming formats, typically the MPEG-2
transport stream format (muxer <code>mpegts</code>).
</p>
<p>For example to remux an MP4 file containing an H.264 stream to mpegts
format with <code>ffmpeg</code>, you can use the command:
</p>
<div class="example">
<pre class="example">ffmpeg -i INPUT.mp4 -codec copy -bsf:v h264_mp4toannexb OUTPUT.ts
</pre></div>

<p>Please note that this filter is auto-inserted for MPEG-TS (muxer
<code>mpegts</code>) and raw H.264 (muxer <code>h264</code>) output formats.
</p>
<a name="h264_005fredundant_005fpps"></a>
<h3 class="section">2.13 h264_redundant_pps<span class="pull-right"><a class="anchor hidden-xs" href="#h264_005fredundant_005fpps" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-h264_005fredundant_005fpps" aria-hidden="true">TOC</a></span></h3>

<p>This applies a specific fixup to some Blu-ray streams which contain
redundant PPSs modifying irrelevant parameters of the stream which
confuse other transformations which require correct extradata.
</p>
<a name="hevc_005fmetadata"></a>
<h3 class="section">2.14 hevc_metadata<span class="pull-right"><a class="anchor hidden-xs" href="#hevc_005fmetadata" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-hevc_005fmetadata" aria-hidden="true">TOC</a></span></h3>

<p>Modify metadata embedded in an HEVC stream.
</p>
<dl compact="compact">
<dt><span><samp>aud</samp></span></dt>
<dd><p>Insert or remove AUD NAL units in all access units of the stream.
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>insert</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>remove</samp>&rsquo;</span></dt>
</dl>

</dd>
<dt><span><samp>sample_aspect_ratio</samp></span></dt>
<dd><p>Set the sample aspect ratio in the stream in the VUI parameters.
</p>
</dd>
<dt><span><samp>video_format</samp></span></dt>
<dt><span><samp>video_full_range_flag</samp></span></dt>
<dd><p>Set the video format in the stream (see H.265 section E.3.1 and
table E.2).
</p>
</dd>
<dt><span><samp>colour_primaries</samp></span></dt>
<dt><span><samp>transfer_characteristics</samp></span></dt>
<dt><span><samp>matrix_coefficients</samp></span></dt>
<dd><p>Set the colour description in the stream (see H.265 section E.3.1
and tables E.3, E.4 and E.5).
</p>
</dd>
<dt><span><samp>chroma_sample_loc_type</samp></span></dt>
<dd><p>Set the chroma sample location in the stream (see H.265 section
E.3.1 and figure E.1).
</p>
</dd>
<dt><span><samp>tick_rate</samp></span></dt>
<dd><p>Set the tick rate in the VPS and VUI parameters (time_scale /
num_units_in_tick). Combined with <samp>num_ticks_poc_diff_one</samp>, this can
set a constant framerate in the stream.  Note that it is likely to be
overridden by container parameters when the stream is in a container.
</p>
</dd>
<dt><span><samp>num_ticks_poc_diff_one</samp></span></dt>
<dd><p>Set poc_proportional_to_timing_flag in VPS and VUI and use this value
to set num_ticks_poc_diff_one_minus1 (see H.265 sections ******* and
E.3.1).  Ignored if <samp>tick_rate</samp> is not also set.
</p>
</dd>
<dt><span><samp>crop_left</samp></span></dt>
<dt><span><samp>crop_right</samp></span></dt>
<dt><span><samp>crop_top</samp></span></dt>
<dt><span><samp>crop_bottom</samp></span></dt>
<dd><p>Set the conformance window cropping offsets in the SPS.  These values
will replace the current ones if the stream is already cropped.
</p>
<p>These fields are set in pixels.  Note that some sizes may not be
representable if the chroma is subsampled (H.265 section 7.4.3.2.1).
</p>
</dd>
<dt><span><samp>level</samp></span></dt>
<dd><p>Set the level in the VPS and SPS.  See H.265 section A.4 and tables
A.6 and A.7.
</p>
<p>The argument must be the name of a level (for example, &lsquo;<samp>5.1</samp>&rsquo;), a
<em>general_level_idc</em> value (for example, &lsquo;<samp>153</samp>&rsquo; for level 5.1),
or the special name &lsquo;<samp>auto</samp>&rsquo; indicating that the filter should
attempt to guess the level from the input stream properties.
</p>
</dd>
</dl>

<a name="hevc_005fmp4toannexb"></a>
<h3 class="section">2.15 hevc_mp4toannexb<span class="pull-right"><a class="anchor hidden-xs" href="#hevc_005fmp4toannexb" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-hevc_005fmp4toannexb" aria-hidden="true">TOC</a></span></h3>

<p>Convert an HEVC/H.265 bitstream from length prefixed mode to start code
prefixed mode (as defined in the Annex B of the ITU-T H.265
specification).
</p>
<p>This is required by some streaming formats, typically the MPEG-2
transport stream format (muxer <code>mpegts</code>).
</p>
<p>For example to remux an MP4 file containing an HEVC stream to mpegts
format with <code>ffmpeg</code>, you can use the command:
</p>
<div class="example">
<pre class="example">ffmpeg -i INPUT.mp4 -codec copy -bsf:v hevc_mp4toannexb OUTPUT.ts
</pre></div>

<p>Please note that this filter is auto-inserted for MPEG-TS (muxer
<code>mpegts</code>) and raw HEVC/H.265 (muxer <code>h265</code> or
<code>hevc</code>) output formats.
</p>
<a name="imxdump"></a>
<h3 class="section">2.16 imxdump<span class="pull-right"><a class="anchor hidden-xs" href="#imxdump" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-imxdump" aria-hidden="true">TOC</a></span></h3>

<p>Modifies the bitstream to fit in MOV and to be usable by the Final Cut
Pro decoder. This filter only applies to the mpeg2video codec, and is
likely not needed for Final Cut Pro 7 and newer with the appropriate
<samp>-tag:v</samp>.
</p>
<p>For example, to remux 30 MB/sec NTSC IMX to MOV:
</p>
<div class="example">
<pre class="example">ffmpeg -i input.mxf -c copy -bsf:v imxdump -tag:v mx3n output.mov
</pre></div>

<a name="mjpeg2jpeg"></a>
<h3 class="section">2.17 mjpeg2jpeg<span class="pull-right"><a class="anchor hidden-xs" href="#mjpeg2jpeg" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-mjpeg2jpeg" aria-hidden="true">TOC</a></span></h3>

<p>Convert MJPEG/AVI1 packets to full JPEG/JFIF packets.
</p>
<p>MJPEG is a video codec wherein each video frame is essentially a
JPEG image. The individual frames can be extracted without loss,
e.g. by
</p>
<div class="example">
<pre class="example">ffmpeg -i ../some_mjpeg.avi -c:v copy frames_%d.jpg
</pre></div>

<p>Unfortunately, these chunks are incomplete JPEG images, because
they lack the DHT segment required for decoding. Quoting from
<a href="http://www.digitalpreservation.gov/formats/fdd/fdd000063.shtml">http://www.digitalpreservation.gov/formats/fdd/fdd000063.shtml</a>:
</p>
<p>Avery Lee, writing in the rec.video.desktop newsgroup in 2001,
commented that &quot;MJPEG, or at least the MJPEG in AVIs having the
MJPG fourcc, is restricted JPEG with a fixed &ndash; and *omitted* &ndash;
Huffman table. The JPEG must be YCbCr colorspace, it must be 4:2:2,
and it must use basic Huffman encoding, not arithmetic or
progressive. . . . You can indeed extract the MJPEG frames and
decode them with a regular JPEG decoder, but you have to prepend
the DHT segment to them, or else the decoder won&rsquo;t have any idea
how to decompress the data. The exact table necessary is given in
the OpenDML spec.&quot;
</p>
<p>This bitstream filter patches the header of frames extracted from an MJPEG
stream (carrying the AVI1 header ID and lacking a DHT segment) to
produce fully qualified JPEG images.
</p>
<div class="example">
<pre class="example">ffmpeg -i mjpeg-movie.avi -c:v copy -bsf:v mjpeg2jpeg frame_%d.jpg
exiftran -i -9 frame*.jpg
ffmpeg -i frame_%d.jpg -c:v copy rotated.avi
</pre></div>

<a name="mjpegadump"></a>
<h3 class="section">2.18 mjpegadump<span class="pull-right"><a class="anchor hidden-xs" href="#mjpegadump" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-mjpegadump" aria-hidden="true">TOC</a></span></h3>

<p>Add an MJPEG A header to the bitstream, to enable decoding by
Quicktime.
</p>
<span id="mov2textsub"></span><a name="mov2textsub-1"></a>
<h3 class="section">2.19 mov2textsub<span class="pull-right"><a class="anchor hidden-xs" href="#mov2textsub-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-mov2textsub-1" aria-hidden="true">TOC</a></span></h3>

<p>Extract a representable text file from MOV subtitles, stripping the
metadata header from each subtitle packet.
</p>
<p>See also the <a href="#text2movsub">text2movsub</a> filter.
</p>
<a name="mp3decomp"></a>
<h3 class="section">2.20 mp3decomp<span class="pull-right"><a class="anchor hidden-xs" href="#mp3decomp" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-mp3decomp" aria-hidden="true">TOC</a></span></h3>

<p>Decompress non-standard compressed MP3 audio headers.
</p>
<a name="mpeg2_005fmetadata"></a>
<h3 class="section">2.21 mpeg2_metadata<span class="pull-right"><a class="anchor hidden-xs" href="#mpeg2_005fmetadata" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-mpeg2_005fmetadata" aria-hidden="true">TOC</a></span></h3>

<p>Modify metadata embedded in an MPEG-2 stream.
</p>
<dl compact="compact">
<dt><span><samp>display_aspect_ratio</samp></span></dt>
<dd><p>Set the display aspect ratio in the stream.
</p>
<p>The following fixed values are supported:
</p><dl compact="compact">
<dt><span><samp>4/3</samp></span></dt>
<dt><span><samp>16/9</samp></span></dt>
<dt><span><samp>221/100</samp></span></dt>
</dl>
<p>Any other value will result in square pixels being signalled instead
(see H.262 section 6.3.3 and table 6-3).
</p>
</dd>
<dt><span><samp>frame_rate</samp></span></dt>
<dd><p>Set the frame rate in the stream.  This is constructed from a table
of known values combined with a small multiplier and divisor - if
the supplied value is not exactly representable, the nearest
representable value will be used instead (see H.262 section 6.3.3
and table 6-4).
</p>
</dd>
<dt><span><samp>video_format</samp></span></dt>
<dd><p>Set the video format in the stream (see H.262 section 6.3.6 and
table 6-6).
</p>
</dd>
<dt><span><samp>colour_primaries</samp></span></dt>
<dt><span><samp>transfer_characteristics</samp></span></dt>
<dt><span><samp>matrix_coefficients</samp></span></dt>
<dd><p>Set the colour description in the stream (see H.262 section 6.3.6
and tables 6-7, 6-8 and 6-9).
</p>
</dd>
</dl>

<a name="mpeg4_005funpack_005fbframes"></a>
<h3 class="section">2.22 mpeg4_unpack_bframes<span class="pull-right"><a class="anchor hidden-xs" href="#mpeg4_005funpack_005fbframes" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-mpeg4_005funpack_005fbframes" aria-hidden="true">TOC</a></span></h3>

<p>Unpack DivX-style packed B-frames.
</p>
<p>DivX-style packed B-frames are not valid MPEG-4 and were only a
workaround for the broken Video for Windows subsystem.
They use more space, can cause minor AV sync issues, require more
CPU power to decode (unless the player has some decoded picture queue
to compensate the 2,0,2,0 frame per packet style) and cause
trouble if copied into a standard container like mp4 or mpeg-ps/ts,
because MPEG-4 decoders may not be able to decode them, since they are
not valid MPEG-4.
</p>
<p>For example to fix an AVI file containing an MPEG-4 stream with
DivX-style packed B-frames using <code>ffmpeg</code>, you can use the command:
</p>
<div class="example">
<pre class="example">ffmpeg -i INPUT.avi -codec copy -bsf:v mpeg4_unpack_bframes OUTPUT.avi
</pre></div>

<a name="noise"></a>
<h3 class="section">2.23 noise<span class="pull-right"><a class="anchor hidden-xs" href="#noise" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-noise" aria-hidden="true">TOC</a></span></h3>

<p>Damages the contents of packets or simply drops them without damaging the
container. Can be used for fuzzing or testing error resilience/concealment.
</p>
<p>Parameters:
</p><dl compact="compact">
<dt><span><samp>amount</samp></span></dt>
<dd><p>Accepts an expression whose evaluation per-packet determines how often bytes in that
packet will be modified. A value below 0 will result in a variable frequency.
Default is 0 which results in no modification. However, if neither amount nor drop is specified,
amount will be set to <var>-1</var>. See below for accepted variables.
</p></dd>
<dt><span><samp>drop</samp></span></dt>
<dd><p>Accepts an expression evaluated per-packet whose value determines whether that packet is dropped.
Evaluation to a positive value results in the packet being dropped. Evaluation to a negative
value results in a variable chance of it being dropped, roughly inverse in proportion to the magnitude
of the value. Default is 0 which results in no drops. See below for accepted variables.
</p></dd>
<dt><span><samp>dropamount</samp></span></dt>
<dd><p>Accepts a non-negative integer, which assigns a variable chance of it being dropped, roughly inverse
in proportion to the value. Default is 0 which results in no drops. This option is kept for backwards
compatibility and is equivalent to setting drop to a negative value with the same magnitude
i.e. <code>dropamount=4</code> is the same as <code>drop=-4</code>. Ignored if drop is also specified.
</p></dd>
</dl>

<p>Both <code>amount</code> and <code>drop</code> accept expressions containing the following variables:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>n</samp>&rsquo;</span></dt>
<dd><p>The index of the packet, starting from zero.
</p></dd>
<dt><span>&lsquo;<samp>tb</samp>&rsquo;</span></dt>
<dd><p>The timebase for packet timestamps.
</p></dd>
<dt><span>&lsquo;<samp>pts</samp>&rsquo;</span></dt>
<dd><p>Packet presentation timestamp.
</p></dd>
<dt><span>&lsquo;<samp>dts</samp>&rsquo;</span></dt>
<dd><p>Packet decoding timestamp.
</p></dd>
<dt><span>&lsquo;<samp>nopts</samp>&rsquo;</span></dt>
<dd><p>Constant representing AV_NOPTS_VALUE.
</p></dd>
<dt><span>&lsquo;<samp>startpts</samp>&rsquo;</span></dt>
<dd><p>First non-AV_NOPTS_VALUE PTS seen in the stream.
</p></dd>
<dt><span>&lsquo;<samp>startdts</samp>&rsquo;</span></dt>
<dd><p>First non-AV_NOPTS_VALUE DTS seen in the stream.
</p></dd>
<dt><span>&lsquo;<samp>duration</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>d</samp>&rsquo;</span></dt>
<dd><p>Packet duration, in timebase units.
</p></dd>
<dt><span>&lsquo;<samp>pos</samp>&rsquo;</span></dt>
<dd><p>Packet position in input; may be -1 when unknown or not set.
</p></dd>
<dt><span>&lsquo;<samp>size</samp>&rsquo;</span></dt>
<dd><p>Packet size, in bytes.
</p></dd>
<dt><span>&lsquo;<samp>key</samp>&rsquo;</span></dt>
<dd><p>Whether packet is marked as a keyframe.
</p></dd>
<dt><span>&lsquo;<samp>state</samp>&rsquo;</span></dt>
<dd><p>A pseudo random integer, primarily derived from the content of packet payload.
</p></dd>
</dl>

<a name="Examples"></a>
<h4 class="subsection">2.23.1 Examples<span class="pull-right"><a class="anchor hidden-xs" href="#Examples" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Examples" aria-hidden="true">TOC</a></span></h4>
<p>Apply modification to every byte but don&rsquo;t drop any packets.
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -c copy -bsf noise=1 output.mkv
</pre></div>

<p>Drop every video packet not marked as a keyframe after timestamp 30s but do not
modify any of the remaining packets.
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -c copy -bsf:v noise=drop='gt(t\,30)*not(key)' output.mkv
</pre></div>

<p>Drop one second of audio every 10 seconds and add some random noise to the rest.
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -c copy -bsf:a noise=amount=-1:drop='between(mod(t\,10)\,9\,10)' output.mkv
</pre></div>

<a name="null"></a>
<h3 class="section">2.24 null<span class="pull-right"><a class="anchor hidden-xs" href="#null" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-null" aria-hidden="true">TOC</a></span></h3>
<p>This bitstream filter passes the packets through unchanged.
</p>
<a name="pcm_005frechunk"></a>
<h3 class="section">2.25 pcm_rechunk<span class="pull-right"><a class="anchor hidden-xs" href="#pcm_005frechunk" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-pcm_005frechunk" aria-hidden="true">TOC</a></span></h3>

<p>Repacketize PCM audio to a fixed number of samples per packet or a fixed packet
rate per second. This is similar to the <a data-manual="ffmpeg-filters" href="ffmpeg-filters.html#asetnsamples">(ffmpeg-filters)asetnsamples audio
filter</a> but works on audio packets instead of audio frames.
</p>
<dl compact="compact">
<dt><span><samp>nb_out_samples, n</samp></span></dt>
<dd><p>Set the number of samples per each output audio packet. The number is intended
as the number of samples <em>per each channel</em>. Default value is 1024.
</p>
</dd>
<dt><span><samp>pad, p</samp></span></dt>
<dd><p>If set to 1, the filter will pad the last audio packet with silence, so that it
will contain the same number of samples (or roughly the same number of samples,
see <samp>frame_rate</samp>) as the previous ones. Default value is 1.
</p>
</dd>
<dt><span><samp>frame_rate, r</samp></span></dt>
<dd><p>This option makes the filter output a fixed number of packets per second instead
of a fixed number of samples per packet. If the audio sample rate is not
divisible by the frame rate then the number of samples will not be constant but
will vary slightly so that each packet will start as close to the frame
boundary as possible. Using this option has precedence over <samp>nb_out_samples</samp>.
</p></dd>
</dl>

<p>You can generate the well known 1602-1601-1602-1601-1602 pattern of 48kHz audio
for NTSC frame rate using the <samp>frame_rate</samp> option.
</p><div class="example">
<pre class="example">ffmpeg -f lavfi -i sine=r=48000:d=1 -c pcm_s16le -bsf pcm_rechunk=r=30000/1001 -f framecrc -
</pre></div>

<a name="pgs_005fframe_005fmerge"></a>
<h3 class="section">2.26 pgs_frame_merge<span class="pull-right"><a class="anchor hidden-xs" href="#pgs_005fframe_005fmerge" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-pgs_005fframe_005fmerge" aria-hidden="true">TOC</a></span></h3>

<p>Merge a sequence of PGS Subtitle segments ending with an &quot;end of display set&quot;
segment into a single packet.
</p>
<p>This is required by some containers that support PGS subtitles
(muxer <code>matroska</code>).
</p>
<a name="prores_005fmetadata"></a>
<h3 class="section">2.27 prores_metadata<span class="pull-right"><a class="anchor hidden-xs" href="#prores_005fmetadata" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-prores_005fmetadata" aria-hidden="true">TOC</a></span></h3>

<p>Modify color property metadata embedded in prores stream.
</p>
<dl compact="compact">
<dt><span><samp>color_primaries</samp></span></dt>
<dd><p>Set the color primaries.
Available values are:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>auto</samp>&rsquo;</span></dt>
<dd><p>Keep the same color primaries property (default).
</p>
</dd>
<dt><span>&lsquo;<samp>unknown</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>bt709</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>bt470bg</samp>&rsquo;</span></dt>
<dd><p>BT601 625
</p>
</dd>
<dt><span>&lsquo;<samp>smpte170m</samp>&rsquo;</span></dt>
<dd><p>BT601 525
</p>
</dd>
<dt><span>&lsquo;<samp>bt2020</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>smpte431</samp>&rsquo;</span></dt>
<dd><p>DCI P3
</p>
</dd>
<dt><span>&lsquo;<samp>smpte432</samp>&rsquo;</span></dt>
<dd><p>P3 D65
</p>
</dd>
</dl>

</dd>
<dt><span><samp>transfer_characteristics</samp></span></dt>
<dd><p>Set the color transfer.
Available values are:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>auto</samp>&rsquo;</span></dt>
<dd><p>Keep the same transfer characteristics property (default).
</p>
</dd>
<dt><span>&lsquo;<samp>unknown</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>bt709</samp>&rsquo;</span></dt>
<dd><p>BT 601, BT 709, BT 2020
</p></dd>
<dt><span>&lsquo;<samp>smpte2084</samp>&rsquo;</span></dt>
<dd><p>SMPTE ST 2084
</p></dd>
<dt><span>&lsquo;<samp>arib-std-b67</samp>&rsquo;</span></dt>
<dd><p>ARIB STD-B67
</p></dd>
</dl>


</dd>
<dt><span><samp>matrix_coefficients</samp></span></dt>
<dd><p>Set the matrix coefficient.
Available values are:
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>auto</samp>&rsquo;</span></dt>
<dd><p>Keep the same colorspace property (default).
</p>
</dd>
<dt><span>&lsquo;<samp>unknown</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>bt709</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>smpte170m</samp>&rsquo;</span></dt>
<dd><p>BT 601
</p>
</dd>
<dt><span>&lsquo;<samp>bt2020nc</samp>&rsquo;</span></dt>
</dl>
</dd>
</dl>

<p>Set Rec709 colorspace for each frame of the file
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -c copy -bsf:v prores_metadata=color_primaries=bt709:color_trc=bt709:colorspace=bt709 output.mov
</pre></div>

<p>Set Hybrid Log-Gamma parameters for each frame of the file
</p><div class="example">
<pre class="example">ffmpeg -i INPUT -c copy -bsf:v prores_metadata=color_primaries=bt2020:color_trc=arib-std-b67:colorspace=bt2020nc output.mov
</pre></div>

<a name="remove_005fextra"></a>
<h3 class="section">2.28 remove_extra<span class="pull-right"><a class="anchor hidden-xs" href="#remove_005fextra" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-remove_005fextra" aria-hidden="true">TOC</a></span></h3>

<p>Remove extradata from packets.
</p>
<p>It accepts the following parameter:
</p><dl compact="compact">
<dt><span><samp>freq</samp></span></dt>
<dd><p>Set which frame types to remove extradata from.
</p>
<dl compact="compact">
<dt><span>&lsquo;<samp>k</samp>&rsquo;</span></dt>
<dd><p>Remove extradata from non-keyframes only.
</p>
</dd>
<dt><span>&lsquo;<samp>keyframe</samp>&rsquo;</span></dt>
<dd><p>Remove extradata from keyframes only.
</p>
</dd>
<dt><span>&lsquo;<samp>e, all</samp>&rsquo;</span></dt>
<dd><p>Remove extradata from all frames.
</p>
</dd>
</dl>
</dd>
</dl>

<a name="setts"></a>
<h3 class="section">2.29 setts<span class="pull-right"><a class="anchor hidden-xs" href="#setts" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-setts" aria-hidden="true">TOC</a></span></h3>
<p>Set PTS and DTS in packets.
</p>
<p>It accepts the following parameters:
</p><dl compact="compact">
<dt><span><samp>ts</samp></span></dt>
<dt><span><samp>pts</samp></span></dt>
<dt><span><samp>dts</samp></span></dt>
<dd><p>Set expressions for PTS, DTS or both.
</p></dd>
<dt><span><samp>duration</samp></span></dt>
<dd><p>Set expression for duration.
</p></dd>
<dt><span><samp>time_base</samp></span></dt>
<dd><p>Set output time base.
</p></dd>
</dl>

<p>The expressions are evaluated through the eval API and can contain the following
constants:
</p>
<dl compact="compact">
<dt><span><samp>N</samp></span></dt>
<dd><p>The count of the input packet. Starting from 0.
</p>
</dd>
<dt><span><samp>TS</samp></span></dt>
<dd><p>The demux timestamp in input in case of <code>ts</code> or <code>dts</code> option or presentation
timestamp in case of <code>pts</code> option.
</p>
</dd>
<dt><span><samp>POS</samp></span></dt>
<dd><p>The original position in the file of the packet, or undefined if undefined
for the current packet
</p>
</dd>
<dt><span><samp>DTS</samp></span></dt>
<dd><p>The demux timestamp in input.
</p>
</dd>
<dt><span><samp>PTS</samp></span></dt>
<dd><p>The presentation timestamp in input.
</p>
</dd>
<dt><span><samp>DURATION</samp></span></dt>
<dd><p>The duration in input.
</p>
</dd>
<dt><span><samp>STARTDTS</samp></span></dt>
<dd><p>The DTS of the first packet.
</p>
</dd>
<dt><span><samp>STARTPTS</samp></span></dt>
<dd><p>The PTS of the first packet.
</p>
</dd>
<dt><span><samp>PREV_INDTS</samp></span></dt>
<dd><p>The previous input DTS.
</p>
</dd>
<dt><span><samp>PREV_INPTS</samp></span></dt>
<dd><p>The previous input PTS.
</p>
</dd>
<dt><span><samp>PREV_INDURATION</samp></span></dt>
<dd><p>The previous input duration.
</p>
</dd>
<dt><span><samp>PREV_OUTDTS</samp></span></dt>
<dd><p>The previous output DTS.
</p>
</dd>
<dt><span><samp>PREV_OUTPTS</samp></span></dt>
<dd><p>The previous output PTS.
</p>
</dd>
<dt><span><samp>PREV_OUTDURATION</samp></span></dt>
<dd><p>The previous output duration.
</p>
</dd>
<dt><span><samp>NEXT_DTS</samp></span></dt>
<dd><p>The next input DTS.
</p>
</dd>
<dt><span><samp>NEXT_PTS</samp></span></dt>
<dd><p>The next input PTS.
</p>
</dd>
<dt><span><samp>NEXT_DURATION</samp></span></dt>
<dd><p>The next input duration.
</p>
</dd>
<dt><span><samp>TB</samp></span></dt>
<dd><p>The timebase of stream packet belongs.
</p>
</dd>
<dt><span><samp>TB_OUT</samp></span></dt>
<dd><p>The output timebase.
</p>
</dd>
<dt><span><samp>SR</samp></span></dt>
<dd><p>The sample rate of stream packet belongs.
</p>
</dd>
<dt><span><samp>NOPTS</samp></span></dt>
<dd><p>The AV_NOPTS_VALUE constant.
</p></dd>
</dl>

<span id="text2movsub"></span><a name="text2movsub-1"></a>
<h3 class="section">2.30 text2movsub<span class="pull-right"><a class="anchor hidden-xs" href="#text2movsub-1" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-text2movsub-1" aria-hidden="true">TOC</a></span></h3>

<p>Convert text subtitles to MOV subtitles (as used by the <code>mov_text</code>
codec) with metadata headers.
</p>
<p>See also the <a href="#mov2textsub">mov2textsub</a> filter.
</p>
<a name="trace_005fheaders"></a>
<h3 class="section">2.31 trace_headers<span class="pull-right"><a class="anchor hidden-xs" href="#trace_005fheaders" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-trace_005fheaders" aria-hidden="true">TOC</a></span></h3>

<p>Log trace output containing all syntax elements in the coded stream
headers (everything above the level of individual coded blocks).
This can be useful for debugging low-level stream issues.
</p>
<p>Supports AV1, H.264, H.265, (M)JPEG, MPEG-2 and VP9, but depending
on the build only a subset of these may be available.
</p>
<a name="truehd_005fcore"></a>
<h3 class="section">2.32 truehd_core<span class="pull-right"><a class="anchor hidden-xs" href="#truehd_005fcore" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-truehd_005fcore" aria-hidden="true">TOC</a></span></h3>

<p>Extract the core from a TrueHD stream, dropping ATMOS data.
</p>
<a name="vp9_005fmetadata"></a>
<h3 class="section">2.33 vp9_metadata<span class="pull-right"><a class="anchor hidden-xs" href="#vp9_005fmetadata" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-vp9_005fmetadata" aria-hidden="true">TOC</a></span></h3>

<p>Modify metadata embedded in a VP9 stream.
</p>
<dl compact="compact">
<dt><span><samp>color_space</samp></span></dt>
<dd><p>Set the color space value in the frame header.  Note that any frame
set to RGB will be implicitly set to PC range and that RGB is
incompatible with profiles 0 and 2.
</p><dl compact="compact">
<dt><span>&lsquo;<samp>unknown</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>bt601</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>bt709</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>smpte170</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>smpte240</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>bt2020</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>rgb</samp>&rsquo;</span></dt>
</dl>

</dd>
<dt><span><samp>color_range</samp></span></dt>
<dd><p>Set the color range value in the frame header.  Note that any value
imposed by the color space will take precedence over this value.
</p><dl compact="compact">
<dt><span>&lsquo;<samp>tv</samp>&rsquo;</span></dt>
<dt><span>&lsquo;<samp>pc</samp>&rsquo;</span></dt>
</dl>
</dd>
</dl>

<a name="vp9_005fsuperframe"></a>
<h3 class="section">2.34 vp9_superframe<span class="pull-right"><a class="anchor hidden-xs" href="#vp9_005fsuperframe" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-vp9_005fsuperframe" aria-hidden="true">TOC</a></span></h3>

<p>Merge VP9 invisible (alt-ref) frames back into VP9 superframes. This
fixes merging of split/segmented VP9 streams where the alt-ref frame
was split from its visible counterpart.
</p>
<a name="vp9_005fsuperframe_005fsplit"></a>
<h3 class="section">2.35 vp9_superframe_split<span class="pull-right"><a class="anchor hidden-xs" href="#vp9_005fsuperframe_005fsplit" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-vp9_005fsuperframe_005fsplit" aria-hidden="true">TOC</a></span></h3>

<p>Split VP9 superframes into single frames.
</p>
<a name="vp9_005fraw_005freorder"></a>
<h3 class="section">2.36 vp9_raw_reorder<span class="pull-right"><a class="anchor hidden-xs" href="#vp9_005fraw_005freorder" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-vp9_005fraw_005freorder" aria-hidden="true">TOC</a></span></h3>

<p>Given a VP9 stream with correct timestamps but possibly out of order,
insert additional show-existing-frame packets to correct the ordering.
</p>

<a name="See-Also"></a>
<h2 class="chapter">3 See Also<span class="pull-right"><a class="anchor hidden-xs" href="#See-Also" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-See-Also" aria-hidden="true">TOC</a></span></h2>

<p><a href="ffmpeg.html">ffmpeg</a>, <a href="ffplay.html">ffplay</a>, <a href="ffprobe.html">ffprobe</a>,
<a href="libavcodec.html">libavcodec</a>
</p>

<a name="Authors"></a>
<h2 class="chapter">4 Authors<span class="pull-right"><a class="anchor hidden-xs" href="#Authors" aria-hidden="true">#</a> <a class="anchor hidden-xs"href="#toc-Authors" aria-hidden="true">TOC</a></span></h2>

<p>The FFmpeg developers.
</p>
<p>For details about the authorship, see the Git history of the project
(https://git.ffmpeg.org/ffmpeg), e.g. by typing the command
<code>git log</code> in the FFmpeg source directory, or browsing the
online repository at <a href="https://git.ffmpeg.org/ffmpeg">https://git.ffmpeg.org/ffmpeg</a>.
</p>
<p>Maintainers for the specific components are listed in the file
<samp>MAINTAINERS</samp> in the source code tree.
</p>

      <p style="font-size: small;">
        This document was generated using <a href="https://www.gnu.org/software/texinfo/"><em>makeinfo</em></a>.
      </p>
    </div>
  </body>
</html>
