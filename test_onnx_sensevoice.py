#!/usr/bin/env python3
# -*- encoding: utf-8 -*-

import numpy as np
import onnxruntime as ort
import librosa
import sys
import os
from pathlib import Path
import sentencepiece as spm

# Add SenseVoice to path
sys.path.append('/media/DataWork/code/sensevoice0825/SenseVoice-main')

def load_audio(file_path, target_sr=16000):
    """Load audio file and convert to target sample rate"""
    audio, sr = librosa.load(file_path, sr=target_sr, mono=True)
    print(f"Audio loaded: {len(audio)} samples, {len(audio)/target_sr:.2f} seconds")
    return audio

def extract_fbank_features(audio, sample_rate=16000, n_mels=80, n_fft=400, hop_length=160):
    """Extract Fbank features similar to SenseVoice preprocessing"""
    # Extract mel spectrogram
    mel_spec = librosa.feature.melspectrogram(
        y=audio,
        sr=sample_rate,
        n_mels=n_mels,
        n_fft=n_fft,
        hop_length=hop_length,
        window='hann',
        center=True,
        pad_mode='reflect'
    )
    
    # Convert to log scale
    log_mel = librosa.power_to_db(mel_spec, ref=np.max)
    
    # Transpose to (time, freq)
    features = log_mel.T
    
    print(f"Fbank features shape: {features.shape}")
    return features

def apply_lfr_cmvn(features, lfr_m=7, lfr_n=6):
    """Apply LFR (Low Frame Rate) and CMVN (Cepstral Mean and Variance Normalization)"""
    # LFR: stack frames
    T, D = features.shape
    
    # Pad features for LFR
    pad_len = (lfr_m - 1) // 2
    padded_features = np.pad(features, ((pad_len, pad_len), (0, 0)), mode='edge')
    
    # Stack frames
    lfr_features = []
    for i in range(0, T, lfr_n):
        if i + lfr_m <= len(padded_features):
            stacked = padded_features[i:i+lfr_m].flatten()
            lfr_features.append(stacked)
    
    lfr_features = np.array(lfr_features)
    
    # Simple CMVN (mean normalization)
    mean = np.mean(lfr_features, axis=0, keepdims=True)
    std = np.std(lfr_features, axis=0, keepdims=True) + 1e-8
    normalized_features = (lfr_features - mean) / std

    print(f"LFR+CMVN features shape: {normalized_features.shape}")
    print(f"Feature range: [{np.min(normalized_features):.3f}, {np.max(normalized_features):.3f}]")
    print(f"Feature mean: {np.mean(normalized_features):.6f}, std: {np.std(normalized_features):.6f}")

    return normalized_features

def test_onnx_sensevoice(model_path, audio_path):
    """Test ONNX SenseVoice model"""
    
    print(f"Loading ONNX model: {model_path}")
    
    # Create ONNX Runtime session
    session = ort.InferenceSession(model_path, providers=['CPUExecutionProvider'])
    
    # Get model input/output info
    input_names = [inp.name for inp in session.get_inputs()]
    output_names = [out.name for out in session.get_outputs()]
    
    print(f"Model inputs: {input_names}")
    print(f"Model outputs: {output_names}")
    
    for inp in session.get_inputs():
        print(f"Input '{inp.name}': shape={inp.shape}, type={inp.type}")
    
    # Load and preprocess audio
    print(f"\nLoading audio: {audio_path}")
    audio = load_audio(audio_path)
    
    # Extract features
    print("\nExtracting features...")
    features = extract_fbank_features(audio)
    
    # Apply LFR and CMVN
    processed_features = apply_lfr_cmvn(features)
    
    # Prepare inputs
    inputs = {}
    
    # Main speech input
    if 'speech' in input_names:
        # Add batch dimension
        speech_input = processed_features[np.newaxis, :, :]  # (1, T, D)
        inputs['speech'] = speech_input.astype(np.float32)
        print(f"Speech input shape: {speech_input.shape}")
    
    # Speech lengths
    if 'speech_lengths' in input_names:
        speech_lengths = np.array([processed_features.shape[0]], dtype=np.int32)
        inputs['speech_lengths'] = speech_lengths
        print(f"Speech lengths: {speech_lengths}")

    # Language ID (Chinese = 3)
    if 'language' in input_names:
        language_id = np.array([3], dtype=np.int32)  # Chinese
        inputs['language'] = language_id
        print(f"Language ID: {language_id}")

    # Text normalization flag
    if 'textnorm' in input_names:
        textnorm = np.array([1], dtype=np.int32)  # Enable text normalization
        inputs['textnorm'] = textnorm
        print(f"Text normalization: {textnorm}")
    
    print(f"\nRunning inference with inputs: {list(inputs.keys())}")
    
    try:
        # Run inference
        outputs = session.run(output_names, inputs)
        
        print(f"\nInference successful!")
        for i, (name, output) in enumerate(zip(output_names, outputs)):
            print(f"Output '{name}': shape={output.shape}, dtype={output.dtype}")
            if output.size < 100:  # Only print small outputs
                print(f"  Values: {output}")
        
        # Try to decode the main output (usually logits)
        if len(outputs) > 0:
            logits = outputs[0]  # Assume first output is logits
            print(f"\nLogits shape: {logits.shape}")
            
            if len(logits.shape) == 3:  # (batch, time, vocab)
                # Simple greedy decoding
                predicted_ids = np.argmax(logits[0], axis=-1)
                print(f"Predicted token IDs: {predicted_ids[:20]}...")  # First 20 tokens
                
                # Remove duplicates (simple CTC-like decoding)
                unique_ids = []
                prev_id = -1
                for token_id in predicted_ids:
                    if token_id != prev_id and token_id != 0:  # 0 is usually blank
                        unique_ids.append(token_id)
                        prev_id = token_id
                
                print(f"Unique token IDs: {unique_ids}")
                print(f"Total unique tokens: {len(unique_ids)}")

                # Print all predicted tokens (not just unique)
                print(f"All predicted tokens (first 30): {predicted_ids[:30].tolist()}")

                # Try to decode with SentencePiece
                try:
                    sp_model_path = "converted_models/openvino/chn_jpn_yue_eng_ko_spectok.bpe.model"
                    if os.path.exists(sp_model_path):
                        sp = spm.SentencePieceProcessor()
                        sp.load(sp_model_path)

                        # Convert to int list if needed
                        token_ids = [int(x) for x in unique_ids]
                        decoded_text = sp.decode(token_ids)
                        print(f"Decoded text: '{decoded_text}'")

                        # Decode the last few tokens separately to see what they are
                        if len(unique_ids) >= 3:
                            last_3_tokens = [int(x) for x in unique_ids[-3:]]
                            try:
                                last_3_decoded = sp.decode(last_3_tokens)
                                print(f"Last 3 tokens {last_3_tokens}: '{last_3_decoded}'")
                            except:
                                print(f"Failed to decode last 3 tokens: {last_3_tokens}")

                        return decoded_text
                    else:
                        print(f"SentencePiece model not found: {sp_model_path}")
                except Exception as e:
                    print(f"SentencePiece decoding failed: {e}")

                return unique_ids
        
    except Exception as e:
        print(f"Inference failed: {e}")
        return None

def main():
    if len(sys.argv) != 3:
        print("Usage: python test_onnx_sensevoice.py <model.onnx> <audio_file>")
        sys.exit(1)
    
    model_path = sys.argv[1]
    audio_path = sys.argv[2]
    
    if not os.path.exists(model_path):
        print(f"Model file not found: {model_path}")
        sys.exit(1)
    
    if not os.path.exists(audio_path):
        print(f"Audio file not found: {audio_path}")
        sys.exit(1)
    
    result = test_onnx_sensevoice(model_path, audio_path)
    
    if result:
        print(f"\nFinal result: {result}")
    else:
        print("\nInference failed")

if __name__ == "__main__":
    main()
